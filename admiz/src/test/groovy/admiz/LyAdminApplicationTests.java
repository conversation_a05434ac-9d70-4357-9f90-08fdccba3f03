package admiz;

import graphql.Assert;
import admiz.system.controller.HealthController;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class LyAdminApplicationTests {

    @Autowired
    private HealthController controller;

    @Test
    void contextLoads() throws Exception {
        Assertions.assertNotNull(controller);
    }

}
