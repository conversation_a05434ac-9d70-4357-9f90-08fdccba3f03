package admiz.utils;

import org.springframework.data.domain.Sort;

import java.util.List;
import java.util.Optional;

public class FormSort extends Sort {
    private Optional<Order> order;

    protected FormSort(List<Order> orders) {
        super(orders);
        this.order = orders.stream().findFirst();
    }

    public static FormSort of(Sort springSort) {
        return new FormSort(springSort.stream().toList()); // 目前只支持一列排序
    }

    public String formValue() {
        return order.map(value -> value.getProperty() + "," + value.getDirection().name()).orElse(null);
    }

    public String property() {
        return order.map(Order::getProperty).orElse(null);
    }

    public String direction() {
        return order.map(x -> x.getDirection().name()).orElse(null);
    }
}
