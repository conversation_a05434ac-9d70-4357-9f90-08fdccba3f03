package admiz.system.model.dto;

import admiz.system.model.SystemResource;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.Nullable;

// 更新系统资源输入DTO
@Data
@NoArgsConstructor
public class UpdateSystemResourceInput {
    
    @NotNull(message = "资源ID不能为空")
    private Long id;
    
    @NotBlank(message = "资源名称不能为空")
    @Size(max = 50, message = "资源名称长度不得超过50字符")
    private String name;
    
    @NotBlank(message = "显示名称不能为空")
    @Size(max = 100, message = "显示名称长度不得超过100字符")
    private String displayName;
    
    @Nullable
    private Long parentId;
    
    @Nullable
    private Integer sortNum = 0;
    
    @NotBlank(message = "权限编码不能为空")
    @Size(max = 60, message = "权限编码长度不得超过60字符")
    private String permission;
    
    @Nullable
    @Size(max = 200, message = "URL长度不得超过200字符")
    private String url = "";
    
    @Nullable
    @Size(max = 30, message = "目标长度不得超过30字符")
    private String target = "";
    
    @NotNull(message = "资源类型不能为空")
    private SystemResource.ResourceType resourceType;
    
    @Nullable
    private Boolean disabled = false;
    
    @Nullable
    private Boolean hidden = false;
    
    @Nullable
    private Boolean autoRefresh = false;
    
    @Nullable
    @Size(max = 60, message = "图标长度不得超过60字符")
    private String icon = "";
    
    @Nullable
    @Size(max = 500, message = "备注长度不得超过500字符")
    private String remark = "";
}
