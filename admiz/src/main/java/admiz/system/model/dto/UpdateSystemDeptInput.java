package admiz.system.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.Nullable;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateSystemDeptInput {

    @NotNull
    private Long id;

    @NotNull
    private Long parentId;

    @NotBlank(message = "部门名称不能为空")
    @Size(max = 50, message = "部门名称长度不得超过50字符")
    private String name;

    @Nullable
    private Integer sortNum;

    @Nullable
    private Long leaderId;

    @Nullable
    @Size(max = 20, message = "联系电话长度不得超过20字符")
    private String phone;

    @Nullable
    @Size(max = 50, message = "邮箱长度不得超过50字符")
    private String email;

    @Nullable
    private Boolean disabled;
}
