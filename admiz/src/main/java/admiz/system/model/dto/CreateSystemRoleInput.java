package admiz.system.model.dto;

import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * 创建系统角色输入DTO
 */
@Data
@NoArgsConstructor
public class CreateSystemRoleInput {
    
    @NotNull
    private String name;
    
    @NotNull
    private String displayName;
    
    @Nullable
    private Integer sortNum = 0;
    
    @Nullable
    @Size(max = 500, message = "备注长度不得超过500字符")
    private String remark;
    
    @Nullable
    private List<Long> resourceIds;
    
    @Nullable
    private Boolean disabled = false;
}
