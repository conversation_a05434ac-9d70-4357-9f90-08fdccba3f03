package admiz.system.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

@Data
@AllArgsConstructor
public class BatchOperationResult {
    private final int successCount;
    private final int failCount;
    private final List<String> errorMessages;

    public boolean isAllSuccess() {
        return failCount == 0;
    }

    public boolean isAllFailed() {
        return successCount == 0;
    }

    public boolean isPartialSuccess() {
        return successCount > 0 && failCount > 0;
    }

    public String getSummaryMessage() {
        return String.format("批量操作完成：成功 %d 个，失败 %d 个", successCount, failCount);
    }

}
