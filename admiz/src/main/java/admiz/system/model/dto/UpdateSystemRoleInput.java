package admiz.system.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * 更新系统角色输入DTO
 */
@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateSystemRoleInput {
    
    @NotNull
    private Long id;
    
    @NotNull
    private String name;
    
    @NotNull
    private String displayName;
    
    @Nullable
    private Integer sortNum;
    
    @Nullable
    @Size(max = 500, message = "备注长度不得超过500字符")
    private String remark;
    
    @Nullable
    private List<Long> resourceIds;
    
    @Nullable
    private Boolean disabled;
}