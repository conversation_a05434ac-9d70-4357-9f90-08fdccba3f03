package admiz.system.service;

import admiz.module.Module;
import admiz.module.ModuleManager;
import admiz.module.ModuleMenu;
import admiz.module.SubSystem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j @RequiredArgsConstructor @Service
public class SystemMenuService {
    final ModuleManager moduleManager;

    public List<SubSystem> getSubSystems() {
        return Arrays.stream(SubSystem.values()).toList();
    }

    public Map<SubSystem, List<ModuleMenu>> getAllSystemMenus() {
        Map<SubSystem, List<ModuleMenu>> allSystemMenus = new HashMap<>();

        List<Module> moduleList = moduleManager.getModules();

        moduleList.forEach(module -> {
            SubSystem subSystem = module.subSystem();
            List<ModuleMenu> moduleMenus = allSystemMenus.get(subSystem);
            if (moduleMenus == null) {
                moduleMenus = new ArrayList<>();
                allSystemMenus.put(subSystem, moduleMenus);
            }

            moduleMenus.addAll(module.menus());
        });


        return allSystemMenus;
    }
}
