package admiz.system.service;

import admiz.system.model.Immutables;
import admiz.system.model.SystemResource;
import admiz.system.model.SystemRole;
import admiz.system.repository.SystemResourceRepository;
import admiz.system.repository.SystemRoleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

// 权限初始化服务
@Slf4j
@Service
@RequiredArgsConstructor
public class SystemPermissionInitService implements ApplicationRunner {

    private final SystemResourceRepository resourceRepository;
    private final SystemRoleRepository roleRepository;

    @Override
    @Transactional
    public void run(ApplicationArguments args) {

         try {
             // 初始化权限资源
             initializeSystemResources();

             // 初始化管理员角色权限
             initializeAdminRolePermissions();

             log.info("系统权限资源初始化完成");
         } catch (Exception e) {
             log.error("系统权限资源初始化失败", e);
         }
    }

    private void initializeSystemResources() {
        // 初始化系统管理模块权限
        initSystemManagementPermissions();
        
        // 初始化用户管理权限
        initUserManagementPermissions();
        
        // 初始化角色管理权限
        initRoleManagementPermissions();
        
        // 初始化资源管理权限
        initResourceManagementPermissions();

        // 初始化审计管理权限
        initAuditManagementPermissions();
    }

    private void initSystemManagementPermissions() {
        // 系统管理根目录
        createResourceIfNotExists("system", "系统管理", null, 
                SystemResource.ResourceType.FOLDER, "system:*", "/system", 100);
        
        // 系统管理菜单
        createResourceIfNotExists("system:mgmt", "系统管理", "system", 
                SystemResource.ResourceType.MENU, "system:mgmt:view", "/system/mgmt", 100);
    }

    private void initUserManagementPermissions() {
        // 用户管理菜单
        createResourceIfNotExists("system:user", "用户管理", "system:mgmt", 
                SystemResource.ResourceType.MENU, "system:user:view", "/system/mgmt/users", 110);
        
        // 用户管理功能权限
        createResourceIfNotExists("system:user:create", "创建用户", "system:user", 
                SystemResource.ResourceType.FUNCTION, "system:user:create", "", 111);
        
        createResourceIfNotExists("system:user:update", "编辑用户", "system:user", 
                SystemResource.ResourceType.FUNCTION, "system:user:update", "", 112);
        
        createResourceIfNotExists("system:user:delete", "删除用户", "system:user", 
                SystemResource.ResourceType.FUNCTION, "system:user:delete", "", 113);
        
        createResourceIfNotExists("system:user:reset-password", "重置密码", "system:user", 
                SystemResource.ResourceType.FUNCTION, "system:user:reset-password", "", 114);
        
        createResourceIfNotExists("system:user:assign-role", "分配角色", "system:user",
                SystemResource.ResourceType.FUNCTION, "system:user:assign-role", "", 115);

        createResourceIfNotExists("system:user:export", "导出用户", "system:user",
                SystemResource.ResourceType.FUNCTION, "system:user:export", "", 116);

        createResourceIfNotExists("system:user:import", "导入用户", "system:user",
                SystemResource.ResourceType.FUNCTION, "system:user:import", "", 117);
    }

    private void initRoleManagementPermissions() {
        // 角色管理菜单
        createResourceIfNotExists("system:role", "角色管理", "system:mgmt", 
                SystemResource.ResourceType.MENU, "system:role:view", "/system/mgmt/roles", 120);
        
        // 角色管理功能权限
        createResourceIfNotExists("system:role:create", "创建角色", "system:role", 
                SystemResource.ResourceType.FUNCTION, "system:role:create", "", 121);
        
        createResourceIfNotExists("system:role:update", "编辑角色", "system:role", 
                SystemResource.ResourceType.FUNCTION, "system:role:update", "", 122);
        
        createResourceIfNotExists("system:role:delete", "删除角色", "system:role", 
                SystemResource.ResourceType.FUNCTION, "system:role:delete", "", 123);
        
        createResourceIfNotExists("system:role:assign-permission", "分配权限", "system:role",
                SystemResource.ResourceType.FUNCTION, "system:role:assign-permission", "", 124);

        createResourceIfNotExists("system:role:export", "导出角色", "system:role",
                SystemResource.ResourceType.FUNCTION, "system:role:export", "", 125);
    }

    private void initResourceManagementPermissions() {
        // 资源管理菜单
        createResourceIfNotExists("system:resource", "资源管理", "system:mgmt", 
                SystemResource.ResourceType.MENU, "system:resource:view", "/system/mgmt/resources", 130);
        
        // 资源管理功能权限
        createResourceIfNotExists("system:resource:create", "创建资源", "system:resource", 
                SystemResource.ResourceType.FUNCTION, "system:resource:create", "", 131);
        
        createResourceIfNotExists("system:resource:update", "编辑资源", "system:resource", 
                SystemResource.ResourceType.FUNCTION, "system:resource:update", "", 132);
        
        createResourceIfNotExists("system:resource:delete", "删除资源", "system:resource",
                SystemResource.ResourceType.FUNCTION, "system:resource:delete", "", 133);

        createResourceIfNotExists("system:resource:export", "导出资源", "system:resource",
                SystemResource.ResourceType.FUNCTION, "system:resource:export", "", 134);
    }


    private void createResourceIfNotExists(String name, String displayName, String parentName, 
                                         SystemResource.ResourceType resourceType, String permission, 
                                         String url, int sortNum) {

        List<SystemResource> existingResources = resourceRepository.findByPermission(permission);
        if (!existingResources.isEmpty()) {
            log.debug("权限资源已存在，跳过创建: {}", permission);
            return;
        }

        Long parentId;
        if (parentName != null) {
            List<SystemResource> parentResources = resourceRepository.findByName(parentName);
            if (!parentResources.isEmpty()) {
                parentId = parentResources.getFirst().id();
            } else {
                parentId = null;
            }
        } else {
            parentId = null;
        }

        SystemResource resource = Immutables.createSystemResource(draft -> {
            draft.setName(name);
            draft.setDisplayName(displayName);
            draft.setParentId(parentId);
            draft.setResourceType(resourceType);
            draft.setPermission(permission);
            draft.setUrl(url);
            draft.setSortNum(sortNum);
            draft.setDisabled(false);
            draft.setHidden(false);
            draft.setAutoRefresh(false);
            draft.setIcon("");
            draft.setTarget("");
            draft.setRemark("系统初始化创建");
        });
        
        try {
            resourceRepository.save(resource);
            log.info("创建权限资源: {} - {}", permission, displayName);
        } catch (Exception e) {
            log.error("创建权限资源失败: {} - {}", permission, displayName, e);
        }
    }

    private void initAuditManagementPermissions() {
        // 审计管理菜单
        createResourceIfNotExists("system:audit", "审计管理", "system:mgmt",
                SystemResource.ResourceType.MENU, "system:audit:view", "/system/mgmt/audit", 140);

        // 审计日志查看
        createResourceIfNotExists("system:audit:logs", "审计日志", "system:audit",
                SystemResource.ResourceType.MENU, "system:audit:logs:view", "/system/mgmt/audit/logs", 141);

        // 审计概览
        createResourceIfNotExists("system:audit:overview", "审计概览", "system:audit",
                SystemResource.ResourceType.MENU, "system:audit:overview:view", "/system/mgmt/audit/overview", 142);

        // 审计管理功能权限
        createResourceIfNotExists("system:audit:export", "导出审计日志", "system:audit",
                SystemResource.ResourceType.FUNCTION, "system:audit:export", "", 143);

        createResourceIfNotExists("system:audit:cleanup", "清理审计日志", "system:audit",
                SystemResource.ResourceType.FUNCTION, "system:audit:cleanup", "", 144);

        createResourceIfNotExists("system:audit:statistics", "审计统计", "system:audit",
                SystemResource.ResourceType.FUNCTION, "system:audit:statistics", "", 145);
    }

    /**
     * 初始化管理员角色权限
     */
    private void initializeAdminRolePermissions() {
        log.info("开始初始化管理员角色权限");

        try {
            SystemRole adminRole = findOrCreateAdminRole();
            if (adminRole == null) {
                log.error("未找到管理员角色，跳过权限分配");
                return;
            }

            List<SystemResource> systemResources = resourceRepository.findAll()
                    .stream()
                    .filter(resource -> resource.permission().startsWith("system:"))
                    .toList();

            if (systemResources.isEmpty()) {
                log.warn("未找到系统管理权限资源，请先初始化权限资源");
                return;
            }

            log.info("找到 {} 个系统管理权限资源:", systemResources.size());
            systemResources.forEach(resource ->
                log.info("  - {} ({}) - {}",
                    resource.displayName(),
                    resource.permission(),
                    resource.resourceType().displayName())
            );

            List<Long> resourceIds = systemResources.stream()
                    .map(SystemResource::id)
                    .collect(Collectors.toList());

            roleRepository.assignResources(adminRole.id(), resourceIds);

            List<String> assignedPermissions = systemResources.stream()
                    .map(SystemResource::permission)
                    .sorted()
                    .collect(Collectors.toList());

            log.info("管理员角色权限分配完成 - 角色: {}, 权限数量: {}",
                    adminRole.name(), resourceIds.size());
            log.info("分配的权限列表: {}", assignedPermissions);

        } catch (Exception e) {
            log.error("初始化管理员角色权限失败", e);
        }
    }

    /**
     * 查找或创建管理员角色
     */
    private SystemRole findOrCreateAdminRole() {
        List<SystemRole> adminRoles = roleRepository.findAll()
                .stream()
                .filter(role -> "ROLE_ADMIN".equals(role.name()) ||
                               "ADMIN".equals(role.name()) ||
                               role.name().toUpperCase().contains("ADMIN"))
                .toList();

        if (!adminRoles.isEmpty()) {
            SystemRole adminRole = adminRoles.getFirst();
            log.info("找到现有管理员角色: {} (ID: {})", adminRole.name(), adminRole.id());
            return adminRole;
        }

        log.info("未找到管理员角色，创建新的管理员角色");
        try {
            SystemRole newAdminRole = Immutables.createSystemRole(draft -> {
                draft.setName("ADMIN");
                draft.setDisplayName("系统管理员");
                draft.setSortNum(0);
                draft.setDisabled(false);
                draft.setDeleted(false);
                draft.setRemark("系统初始化创建的管理员角色");
            });

            SystemRole savedRole = roleRepository.save(newAdminRole);
            log.info("创建管理员角色成功: {} (ID: {})", savedRole.name(), savedRole.id());
            return savedRole;

        } catch (Exception e) {
            log.error("创建管理员角色失败", e);
            return null;
        }
    }
}
