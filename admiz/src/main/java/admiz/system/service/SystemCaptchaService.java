package admiz.system.service;

import admiz.common.errorhandling.AppErrors;
import admiz.common.errorhandling.ErrorTag;
import admiz.common.setting.ApplicationSetting;
import admiz.system.model.Immutables;
import admiz.system.model.SystemKvStore;
import admiz.system.repository.SystemKvStoreRepository;
import com.google.code.kaptcha.impl.DefaultKaptcha;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.Serial;
import java.io.UncheckedIOException;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.Optional;

@Slf4j
@RequiredArgsConstructor @Service
public class SystemCaptchaService {
    final SystemKvStoreRepository kvStoreRepository;
    final DefaultKaptcha textCaptchaProducer;
    final ApplicationSetting appSetting;

    @Value("${mfa.kaptcha.expireDurationMinutes:10}")
    int expireDurationMinutes;

    public BufferedImage createCaptcha(String sessionId) {
        String captchaText = textCaptchaProducer.createText();
        SystemKvStore captcha = Immutables.createSystemKvStore(draft -> {
            draft.setId(sessionId)
                    .setData(captchaText)
                    .setCreateTime(LocalDateTime.now())
                    .setExpireTime(LocalDateTime.now().plusMinutes(expireDurationMinutes));
        });
        kvStoreRepository.save(captcha);

        BufferedImage image = textCaptchaProducer.createImage(captchaText);
        return image;
    }

    public boolean validateCaptcha(String sessionId, String input) {
        if(appSetting.isDevEnvironment()) {
            log.warn("当前是开发环境，验证码直接绕过判断，方便开发测试");
            return true;
        }

        if(Strings.isBlank(input)) {
            kvStoreRepository.deleteById(sessionId);
            return false;
        }

        Optional<SystemKvStore> findItemResult = kvStoreRepository.findById(sessionId);
        if(findItemResult.isEmpty()) {
            return false;
        }

        SystemKvStore kvItem = findItemResult.get();
        kvStoreRepository.deleteById(kvItem.id());

        return input.equals(kvItem.data());
    }
}
