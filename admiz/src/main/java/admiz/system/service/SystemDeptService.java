package admiz.system.service;

import admiz.system.model.SystemDept;
import admiz.system.model.SystemUser;
import admiz.system.model.dto.AssignUserDeptInput;
import admiz.system.model.dto.CreateSystemDeptInput;
import admiz.system.model.dto.UpdateSystemDeptInput;
import org.babyfish.jimmer.sql.fetcher.Fetcher;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

public interface SystemDeptService {

    // 创建部门
    SystemDept createDept(@NotNull CreateSystemDeptInput input);

    // 更新部门
    SystemDept updateDept(@NotNull UpdateSystemDeptInput input);

    // 删除部门
    boolean deleteDept(@NotNull Long deptId);

    // 根据ID查询部门
    Optional<SystemDept> findById(@NotNull Long deptId, @Nullable Fetcher<SystemDept> fetcher);

    // 根据ID查询部门（必须存在）
    SystemDept getById(@NotNull Long deptId, @Nullable Fetcher<SystemDept> fetcher);

    // 根据名称查询部门
    Optional<SystemDept> findByName(@NotNull String name);

    // 检查部门名称是否存在
    boolean existsByName(@NotNull String name);

    // 检查部门名称是否存在（排除指定ID）
    boolean existsByNameAndIdNot(@NotNull String name, @Nullable Long excludeId);

    // 分页查询部门列表
    Page<SystemDept> paginate(@Nullable String searchText,
                              @Nullable Boolean disabled,
                              @NotNull Pageable pageable,
                              @Nullable Fetcher<SystemDept> fetcher);

    // 查询所有可用部门
    List<SystemDept> findAllAvailable(@Nullable Fetcher<SystemDept> fetcher);

    // 构建部门树结构
    List<SystemDept> buildDeptTree(@Nullable Fetcher<SystemDept> fetcher);

    // 根据父部门ID查询子部门
    List<SystemDept> findByParentId(@NotNull Long parentId, @Nullable Fetcher<SystemDept> fetcher);

    // 获取部门的所有祖先部门
    List<SystemDept> getDeptAncestors(@NotNull Long deptId);

    // 获取部门的所有子孙部门
    List<SystemDept> getDeptDescendants(@NotNull Long deptId);

    // 用户部门关联管理
    void assignUserToDepts(@NotNull AssignUserDeptInput input);

    void removeUserFromDept(@NotNull Long userId, @NotNull Long deptId);

    void removeUserFromAllDepts(@NotNull Long userId);

    // 查询部门下的用户
    List<SystemUser> getDeptUsers(@NotNull Long deptId, @Nullable Fetcher<SystemUser> fetcher);

    // 查询用户所在的部门
    List<SystemDept> getUserDepts(@NotNull Long userId, @Nullable Fetcher<SystemDept> fetcher);

    // 获取用户的主要部门（第一个部门）
    Optional<SystemDept> getUserPrimaryDept(@NotNull Long userId);
}
