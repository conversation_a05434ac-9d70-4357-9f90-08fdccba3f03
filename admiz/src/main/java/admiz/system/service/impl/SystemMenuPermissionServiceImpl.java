package admiz.system.service.impl;

import admiz.system.model.SystemResource;
import admiz.system.service.SystemMenuPermissionService;
import admiz.system.service.SystemPermissionService;
import admiz.system.service.SystemResourceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class SystemMenuPermissionServiceImpl implements SystemMenuPermissionService {
    
    private final SystemResourceService resourceService;
    private final SystemPermissionService permissionService;

    @Override
    public List<SystemResource> getUserMenuTree(@NotNull Long userId) {
        try {
            List<SystemResource> userMenus = resourceService.findUserMenuResources(userId, null);
            return buildMenuTree(userMenus);
        } catch (Exception e) {
            log.error("获取用户菜单树失败: userId={}", userId, e);
            return List.of();
        }
    }

    @Override
    public List<SystemResource> getCurrentUserMenuTree() {
        Long currentUserId = permissionService.getCurrentUserId();
        if (currentUserId == null) {
            return List.of();
        }
        return getUserMenuTree(currentUserId);
    }

    @Override
    public List<SystemResource> buildMenuTree(@NotNull List<SystemResource> menuResources) {
        if (menuResources.isEmpty()) {
            return List.of();
        }
        
        // 按父级ID分组
        Map<Long, List<SystemResource>> menuMap = menuResources.stream()
                .filter(menu -> !menu.hidden()) // 过滤隐藏的菜单
                .collect(Collectors.groupingBy(
                    menu -> menu.parentId() != null ? menu.parentId() : 0L
                ));
        
        // 构建树结构
        return buildMenuTreeRecursive(menuMap, 0L);
    }

    private List<SystemResource> buildMenuTreeRecursive(Map<Long, List<SystemResource>> menuMap, Long parentId) {
        List<SystemResource> children = menuMap.get(parentId);
        if (children == null || children.isEmpty()) {
            return List.of();
        }
        
        return children.stream()
                .sorted(Comparator.comparing(SystemResource::sortNum))
                .map(menu -> {
                    List<SystemResource> subMenus = buildMenuTreeRecursive(menuMap, menu.id());
                    return menu;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<SystemResource> filterVisibleMenus(@NotNull Long userId, @NotNull List<SystemResource> allMenus) {
        // 获取用户拥有的所有权限
        Set<String> userPermissions = resourceService.findUserResources(userId, null)
                .stream()
                .map(SystemResource::permission)
                .collect(Collectors.toSet());
        
        return allMenus.stream()
                .filter(menu -> !menu.hidden()) // 过滤隐藏菜单
                .filter(menu -> !menu.disabled()) // 过滤禁用菜单
                .filter(menu -> userPermissions.contains(menu.permission())) // 检查权限
                .collect(Collectors.toList());
    }

    @Override
    public boolean isMenuVisible(@NotNull Long userId, @NotNull Long menuId) {
        try {
            SystemResource menu = resourceService.getById(menuId, null);
            
            // 检查菜单是否隐藏或禁用
            if (menu.hidden() || menu.disabled()) {
                return false;
            }
            
            // 检查用户是否有权限访问
            return permissionService.hasPermission(userId, menu.permission());
        } catch (Exception e) {
            log.error("检查菜单可见性失败: userId={}, menuId={}", userId, menuId, e);
            return false;
        }
    }

    @Override
    public List<SystemResource> getUserTopLevelMenus(@NotNull Long userId) {
        try {
            List<SystemResource> userMenus = resourceService.findUserMenuResources(userId, null);
            
            return userMenus.stream()
                    .filter(menu -> menu.parentId() == null) // 顶级菜单
                    .filter(menu -> !menu.hidden() && !menu.disabled())
                    .sorted(Comparator.comparing(SystemResource::sortNum))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取用户顶级菜单失败: userId={}", userId, e);
            return List.of();
        }
    }

    @Override
    public List<SystemResource> getSubMenus(@NotNull Long parentMenuId, @Nullable Long userId) {
        try {
            List<SystemResource> subMenus = resourceService.findByParentId(parentMenuId, null)
                    .stream()
                    .filter(menu -> menu.resourceType() == SystemResource.ResourceType.MENU)
                    .filter(menu -> !menu.hidden() && !menu.disabled())
                    .collect(Collectors.toList());
            
            // 如果指定了用户ID，则过滤用户有权限的菜单
            if (userId != null) {
                Set<String> userPermissions = resourceService.findUserResources(userId, null)
                        .stream()
                        .map(SystemResource::permission)
                        .collect(Collectors.toSet());
                
                subMenus = subMenus.stream()
                        .filter(menu -> userPermissions.contains(menu.permission()))
                        .collect(Collectors.toList());
            }
            
            return subMenus.stream()
                    .sorted(Comparator.comparing(SystemResource::sortNum))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取子菜单失败: parentMenuId={}, userId={}", parentMenuId, userId, e);
            return List.of();
        }
    }
}
