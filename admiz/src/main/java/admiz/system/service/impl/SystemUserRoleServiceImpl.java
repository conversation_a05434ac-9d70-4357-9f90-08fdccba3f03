package admiz.system.service.impl;

import admiz.auth.AuthContext;
import admiz.common.errorhandling.AppErrors;
import admiz.common.errorhandling.ErrorDetailException;
import admiz.system.model.SystemRole;
import admiz.system.model.SystemUser;
import admiz.system.repository.SystemRoleRepository;
import admiz.system.repository.SystemUserRepository;
import admiz.system.service.SystemPermissionAuditService;
import admiz.system.service.SystemUserRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Service
public class SystemUserRoleServiceImpl implements SystemUserRoleService {
    
    private final SystemUserRepository userRepository;
    private final SystemRoleRepository roleRepository;
    private final SystemPermissionAuditService auditService;

    @Override
    public List<SystemRole> getUserRoles(@NotNull Long userId) {

        SystemUser user = userRepository.findById(userId)
                .orElseThrow(() -> AppErrors.CommonError.NOT_FOUND.toException("用户不存在: " + userId));
        
        return userRepository.getUserRoles(userId);
    }

    @Override
    @Transactional
    public boolean assignRoles(@NotNull Long userId, @NotNull List<Long> roleIds) {

        try {
            SystemUser user = userRepository.findById(userId)
                    .orElseThrow(() -> AppErrors.CommonError.NOT_FOUND.toException("用户不存在: " + userId));

            if (user.isBuiltinAdmin() && !roleIds.contains(1L)) {
                throw AppErrors.CommonError.FORBIDDEN.toException("不能移除系统内置管理员的管理员角色");
            }

            List<SystemRole> roles = roleRepository.findByIds(roleIds);
            if (roles.size() != roleIds.size()) {
                throw AppErrors.CommonError.INVALID_PARAMETER.toException("部分角色不存在");
            }

            userRepository.assignRoles(userId, roleIds);

            // 记录审计日志
            try {
                Long operatorUserId = AuthContext.currentUser().id();
                String operatorUserLogin = AuthContext.currentUser().login();
                List<String> roleNames = roles.stream().map(SystemRole::name).toList();
                auditService.logUserRoleAssign(operatorUserId, operatorUserLogin,
                        userId, user.login(), roleIds, roleNames, true, null);
            } catch (Exception auditEx) {
                log.warn("记录用户角色分配审计日志失败", auditEx);
            }

            log.info("用户角色分配成功: userId={}, roleCount={}, operator={}",
                    userId, roleIds.size(), getCurrentUserInfo());

            return true;
        } catch (ErrorDetailException e) {
            throw e;
        } catch (Exception ex) {
            log.error("用户角色分配失败: userId={}, operator={}", 
                    userId, getCurrentUserInfo(), ex);
            throw AppErrors.CommonError.SERVER_ERROR.toException();
        }
    }

    @Override
    @Transactional
    public boolean removeRoles(@NotNull Long userId, @NotNull List<Long> roleIds) {

        try {
            SystemUser user = userRepository.findById(userId)
                    .orElseThrow(() -> AppErrors.CommonError.NOT_FOUND.toException("用户不存在: " + userId));

            if (user.isBuiltinAdmin() && roleIds.contains(1L)) {
                throw AppErrors.CommonError.FORBIDDEN.toException("不能移除系统内置管理员的管理员角色");
            }

            List<SystemRole> roles = roleRepository.findByIds(roleIds);
            userRepository.removeRoles(userId, roleIds);

            // 记录审计日志
            try {
                Long operatorUserId = AuthContext.currentUser().id();
                String operatorUserLogin = AuthContext.currentUser().login();
                List<String> roleNames = roles.stream().map(SystemRole::name).toList();
                auditService.logUserRoleRemove(operatorUserId, operatorUserLogin,
                        userId, user.login(), roleIds, roleNames, true, null);
            } catch (Exception auditEx) {
                log.warn("记录用户角色移除审计日志失败", auditEx);
            }

            log.info("用户角色移除成功: userId={}, roleCount={}, operator={}",
                    userId, roleIds.size(), getCurrentUserInfo());

            return true;
        } catch (ErrorDetailException e) {
            throw e;
        } catch (Exception ex) {
            log.error("用户角色移除失败: userId={}, operator={}", 
                    userId, getCurrentUserInfo(), ex);
            throw AppErrors.CommonError.SERVER_ERROR.toException();
        }
    }

    @Override
    @Transactional
    public boolean batchAssignRoles(@NotNull List<Long> userIds, @NotNull List<Long> roleIds) {
        if (userIds.isEmpty()) {
            throw AppErrors.CommonError.INVALID_PARAMETER.toException("用户ID列表不能为空");
        }

        try {
            List<SystemRole> roles = roleRepository.findByIds(roleIds);
            if (roles.size() != roleIds.size()) {
                throw AppErrors.CommonError.INVALID_PARAMETER.toException("部分角色不存在");
            }

            int successCount = 0;
            int failCount = 0;

            for (Long userId : userIds) {
                try {
                    SystemUser user = userRepository.findById(userId).orElse(null);
                    if (user == null) {
                        log.warn("批量分配角色时用户不存在: userId={}", userId);
                        failCount++;
                        continue;
                    }

                    if (user.isBuiltinAdmin() && !roleIds.contains(1L)) {
                        log.warn("尝试移除系统内置管理员的管理员角色: userId={}", userId);
                        failCount++;
                        continue;
                    }

                    userRepository.assignRoles(userId, roleIds);
                    successCount++;
                } catch (Exception e) {
                    log.warn("批量分配角色失败: userId={}", userId, e);
                    failCount++;
                }
            }

            log.info("批量角色分配完成: 成功={}, 失败={}, operator={}", 
                    successCount, failCount, getCurrentUserInfo());
            
            return failCount == 0;
        } catch (ErrorDetailException e) {
            throw e;
        } catch (Exception ex) {
            log.error("批量角色分配失败: operator={}", getCurrentUserInfo(), ex);
            throw AppErrors.CommonError.SERVER_ERROR.toException();
        }
    }

    @Override
    @Transactional
    public boolean batchRemoveRoles(@NotNull List<Long> userIds, @NotNull List<Long> roleIds) {
        if (userIds.isEmpty()) {
            throw AppErrors.CommonError.INVALID_PARAMETER.toException("用户ID列表不能为空");
        }

        try {
            List<SystemRole> roles = roleRepository.findByIds(roleIds);
            if (roles.size() != roleIds.size()) {
                throw AppErrors.CommonError.INVALID_PARAMETER.toException("部分角色不存在");
            }

            int successCount = 0;
            int failCount = 0;

            for (Long userId : userIds) {
                try {
                    SystemUser user = userRepository.findById(userId).orElse(null);
                    if (user == null) {
                        log.warn("批量移除角色时用户不存在: userId={}", userId);
                        failCount++;
                        continue;
                    }

                    if (user.isBuiltinAdmin() && roleIds.contains(1L)) {
                        log.warn("尝试移除系统内置管理员的管理员角色: userId={}", userId);
                        failCount++;
                        continue;
                    }

                    userRepository.removeRoles(userId, roleIds);
                    successCount++;
                } catch (Exception e) {
                    log.warn("批量移除角色失败: userId={}", userId, e);
                    failCount++;
                }
            }

            log.info("批量角色移除完成: 成功={}, 失败={}, operator={}",
                    successCount, failCount, getCurrentUserInfo());

            return failCount == 0;
        } catch (ErrorDetailException e) {
            throw e;
        } catch (Exception ex) {
            log.error("批量角色移除失败: operator={}", getCurrentUserInfo(), ex);
            throw AppErrors.CommonError.SERVER_ERROR.toException();
        }
    }

    @Override
    @Transactional
    public boolean batchAddRoles(@NotNull List<Long> userIds, @NotNull List<Long> roleIds) {
        if (userIds.isEmpty()) {
            throw AppErrors.CommonError.INVALID_PARAMETER.toException("用户ID列表不能为空");
        }

        try {
            List<SystemRole> roles = roleRepository.findByIds(roleIds);
            if (roles.size() != roleIds.size()) {
                throw AppErrors.CommonError.INVALID_PARAMETER.toException("部分角色不存在");
            }

            int successCount = 0;
            int failCount = 0;

            for (Long userId : userIds) {
                try {
                    SystemUser user = userRepository.findById(userId).orElse(null);
                    if (user == null) {
                        log.warn("批量添加角色时用户不存在: userId={}", userId);
                        failCount++;
                        continue;
                    }

                    // 获取用户当前角色
                    List<SystemRole> currentRoles = userRepository.getUserRoles(userId);
                    List<Long> currentRoleIds = currentRoles.stream().map(SystemRole::id).toList();

                    // 合并角色ID，避免重复
                    List<Long> newRoleIds = currentRoleIds.stream()
                            .filter(id -> !roleIds.contains(id))
                            .collect(java.util.stream.Collectors.toList());
                    newRoleIds.addAll(roleIds);

                    userRepository.assignRoles(userId, newRoleIds);
                    successCount++;
                } catch (Exception e) {
                    log.warn("批量添加角色失败: userId={}", userId, e);
                    failCount++;
                }
            }

            log.info("批量角色添加完成: 成功={}, 失败={}, operator={}",
                    successCount, failCount, getCurrentUserInfo());

            return failCount == 0;
        } catch (ErrorDetailException e) {
            throw e;
        } catch (Exception ex) {
            log.error("批量角色添加失败: operator={}", getCurrentUserInfo(), ex);
            throw AppErrors.CommonError.SERVER_ERROR.toException();
        }
    }

    private String getCurrentUserInfo() {
        try {
            return AuthContext.currentUser().toStringInfo();
        } catch (Exception e) {
            return "系统";
        }
    }
}
