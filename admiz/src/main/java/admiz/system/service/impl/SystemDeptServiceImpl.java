package admiz.system.service.impl;

import admiz.auth.AuthContext;
import admiz.common.errorhandling.AppErrors;
import admiz.common.errorhandling.ErrorDetailException;
import admiz.system.model.Immutables;
import admiz.system.model.SystemDept;
import admiz.system.model.SystemDeptUser;
import admiz.system.model.SystemUser;
import admiz.system.model.dto.AssignUserDeptInput;
import admiz.system.model.dto.CreateSystemDeptInput;
import admiz.system.model.dto.UpdateSystemDeptInput;
import admiz.system.repository.SystemDeptRepository;
import admiz.system.repository.SystemDeptUserRepository;
import admiz.system.repository.SystemUserRepository;
import admiz.system.service.SystemDeptService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.babyfish.jimmer.sql.ast.mutation.SaveMode;
import org.babyfish.jimmer.sql.fetcher.Fetcher;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@RequiredArgsConstructor
@Service
public class SystemDeptServiceImpl implements SystemDeptService {

    private final SystemDeptRepository deptRepository;
    private final SystemDeptUserRepository deptUserRepository;
    private final SystemUserRepository userRepository;

    @Transactional
    public SystemDept createDept(@NotNull CreateSystemDeptInput input) {
        try {
            // 检查部门名称是否已存在
            if (existsByName(input.getName())) {
                throw AppErrors.CommonError.INVALID_PARAMETER.toException("部门名称已存在: " + input.getName());
            }

            // 构建ancestors字段
            String ancestors = buildAncestors(input.getParentId());

            // 创建部门对象
            SystemDept dept = Immutables.createSystemDept(draft -> {
                draft.setParentId(input.getParentId());
                draft.setAncestors(ancestors);
                draft.setName(input.getName());
                draft.setSortNum(input.getSortNum() != null ? input.getSortNum() : 0);
                draft.setLeaderId(input.getLeaderId() != null ? input.getLeaderId() : 0L);
                draft.setPhone(input.getPhone() != null ? input.getPhone() : "");
                draft.setEmail(input.getEmail() != null ? input.getEmail() : "");
                draft.setDisabled(input.getDisabled() != null ? input.getDisabled() : false);
                draft.setDeleted(false);
            });

            SystemDept savedDept = deptRepository.save(dept, SaveMode.INSERT_ONLY);

            log.info("创建部门成功: deptId={}, name={}, operator={}",
                    savedDept.id(), savedDept.name(), getCurrentUserInfo());

            return savedDept;
        } catch (ErrorDetailException e) {
            throw e;
        } catch (Exception ex) {
            log.error("创建部门失败: name={}, operator={}",
                    input.getName(), getCurrentUserInfo(), ex);
            throw AppErrors.CommonError.SERVER_ERROR.toException();
        }
    }

    @Transactional
    public SystemDept updateDept(@NotNull UpdateSystemDeptInput input) {
        try {
            SystemDept existingDept = deptRepository.findById(input.getId())
                    .orElseThrow(() -> AppErrors.CommonError.NOT_FOUND.toException("部门不存在: " + input.getId()));

            // 检查是否修改为自己的子部门（防止循环引用）
            if (!input.getParentId().equals(existingDept.parentId()) &&
                    isDescendantOf(input.getParentId(), input.getId())) {
                throw AppErrors.CommonError.INVALID_PARAMETER.toException("不能将部门移动到自己的子部门下");
            }

            // 名称唯一性检查
            if (!existingDept.name().equals(input.getName()) &&
                    existsByNameAndIdNot(input.getName(), input.getId())) {
                throw AppErrors.CommonError.INVALID_PARAMETER.toException("部门名称已存在: " + input.getName());
            }

            // 构建新的ancestors字段
            String ancestors = buildAncestors(input.getParentId());

            SystemDept updatedDept = Immutables.createSystemDept(draft -> {
                draft.setId(input.getId());
                draft.setParentId(input.getParentId());
                draft.setAncestors(ancestors);
                draft.setName(input.getName());
                draft.setSortNum(input.getSortNum() != null ? input.getSortNum() : existingDept.sortNum());
                draft.setLeaderId(input.getLeaderId() != null ? input.getLeaderId() : existingDept.leaderId());
                draft.setPhone(input.getPhone() != null ? input.getPhone() : existingDept.phone());
                draft.setEmail(input.getEmail() != null ? input.getEmail() : existingDept.email());
                draft.setDisabled(input.getDisabled() != null ? input.getDisabled() : existingDept.disabled());
            });

            SystemDept savedDept = deptRepository.save(updatedDept);

            // 如果父部门发生变化，需要更新所有子部门的ancestors字段
            if (!input.getParentId().equals(existingDept.parentId())) {
                updateChildDeptAncestors(input.getId());
            }

            log.info("更新部门成功: deptId={}, name={}, operator={}",
                    savedDept.id(), savedDept.name(), getCurrentUserInfo());

            return savedDept;
        } catch (ErrorDetailException e) {
            throw e;
        } catch (Exception ex) {
            log.error("更新部门失败: deptId={}, name={}, operator={}",
                    input.getId(), input.getName(), getCurrentUserInfo(), ex);
            throw AppErrors.CommonError.SERVER_ERROR.toException();
        }
    }

    @Transactional
    public boolean deleteDept(@NotNull Long deptId) {
        try {
            SystemDept dept = deptRepository.findById(deptId)
                    .orElseThrow(() -> AppErrors.CommonError.NOT_FOUND.toException("部门不存在: " + deptId));

            // 检查是否有子部门
            List<SystemDept> childDepts = findByParentId(deptId, null);
            if (!childDepts.isEmpty()) {
                throw AppErrors.CommonError.INVALID_PARAMETER.toException("部门下还有子部门，无法删除");
            }

            // 检查是否有用户
            List<SystemUser> deptUsers = getDeptUsers(deptId, null);
            if (!deptUsers.isEmpty()) {
                throw AppErrors.CommonError.INVALID_PARAMETER.toException("部门下还有用户，无法删除");
            }

            // 软删除部门
            SystemDept deletedDept = Immutables.createSystemDept(draft -> {
                draft.setId(deptId);
                draft.setDeleted(true);
            });

            deptRepository.save(deletedDept);

            log.info("删除部门成功: deptId={}, name={}, operator={}",
                    deptId, dept.name(), getCurrentUserInfo());

            return true;
        } catch (ErrorDetailException e) {
            throw e;
        } catch (Exception ex) {
            log.error("删除部门失败: deptId={}, operator={}",
                    deptId, getCurrentUserInfo(), ex);
            throw AppErrors.CommonError.SERVER_ERROR.toException();
        }
    }

    public Optional<SystemDept> findById(@NotNull Long deptId, @Nullable Fetcher<SystemDept> fetcher) {
        return deptRepository.findById(deptId, fetcher);
    }

    public SystemDept getById(@NotNull Long deptId, @Nullable Fetcher<SystemDept> fetcher) {
        return findById(deptId, fetcher)
                .orElseThrow(() -> AppErrors.CommonError.NOT_FOUND.toException("部门不存在: " + deptId));
    }

    public Optional<SystemDept> findByName(@NotNull String name) {
        return deptRepository.findByName(name, null);
    }

    public boolean existsByName(@NotNull String name) {
        return deptRepository.existsByName(name);
    }

    public boolean existsByNameAndIdNot(@NotNull String name, @Nullable Long excludeId) {
        return deptRepository.existsByNameAndIdNot(name, excludeId);
    }

    public Page<SystemDept> paginate(@Nullable String searchText,
                                     @Nullable Boolean disabled,
                                     @NotNull Pageable pageable,
                                     @Nullable Fetcher<SystemDept> fetcher) {
        return deptRepository.paginate(searchText, disabled, pageable, fetcher);
    }

    public List<SystemDept> findAllAvailable(@Nullable Fetcher<SystemDept> fetcher) {
        return deptRepository.findAllAvailable(fetcher);
    }

    public List<SystemDept> buildDeptTree(@Nullable Fetcher<SystemDept> fetcher) {
        return deptRepository.findDeptTree(fetcher);
    }

    public List<SystemDept> findByParentId(@NotNull Long parentId, @Nullable Fetcher<SystemDept> fetcher) {
        return deptRepository.findByParentId(parentId, fetcher);
    }

    public List<SystemDept> getDeptAncestors(@NotNull Long deptId) {

        SystemDept dept = getById(deptId, null);
        List<SystemDept> ancestors = new ArrayList<>();

        if (dept.ancestors() != null && !dept.ancestors().isEmpty()) {
            String[] ancestorIds = dept.ancestors().split(",");
            for (String ancestorId : ancestorIds) {
                if (!ancestorId.trim().isEmpty() && !ancestorId.equals("0")) {
                    try {
                        Long id = Long.parseLong(ancestorId.trim());
                        findById(id, null).ifPresent(ancestors::add);
                    } catch (NumberFormatException e) {
                        log.warn("无效的祖先部门ID: {}", ancestorId);
                    }
                }
            }
        }

        return ancestors;
    }

    public List<SystemDept> getDeptDescendants(@NotNull Long deptId) {
        List<SystemDept> allDepts = deptRepository.findDeptTree(null);
        List<SystemDept> descendants = new ArrayList<>();

        for (SystemDept dept : allDepts) {
            if (dept.ancestors() != null && dept.ancestors().contains(String.valueOf(deptId))) {
                descendants.add(dept);
            }
        }

        return descendants;
    }

    @Transactional
    public void assignUserToDepts(@NotNull AssignUserDeptInput input) {
        try {
            // 检查用户是否存在
            userRepository.findById(input.getUserId())
                    .orElseThrow(() -> AppErrors.CommonError.NOT_FOUND.toException("用户不存在: " + input.getUserId()));

            // 检查所有部门是否存在
            for (Long deptId : input.getDeptIds()) {
                getById(deptId, null);
            }

            // 删除用户现有的部门关联
            deptUserRepository.deleteByUserId(input.getUserId());

            // 创建新的部门关联
            for (Long deptId : input.getDeptIds()) {
                if (!deptUserRepository.existsByUserIdAndDeptId(input.getUserId(), deptId)) {
                    SystemDeptUser deptUser = Immutables.createSystemDeptUser(draft -> {
                        draft.setUserId(input.getUserId());
                        draft.setDeptId(deptId);
                    });
                    deptUserRepository.save(deptUser, SaveMode.INSERT_ONLY);
                }
            }

            log.info("用户部门分配成功: userId={}, deptCount={}, operator={}",
                    input.getUserId(), input.getDeptIds().size(), getCurrentUserInfo());

        } catch (ErrorDetailException e) {
            throw e;
        } catch (Exception ex) {
            log.error("用户部门分配失败: userId={}, operator={}",
                    input.getUserId(), getCurrentUserInfo(), ex);
            throw AppErrors.CommonError.SERVER_ERROR.toException();
        }
    }

    @Transactional
    public void removeUserFromDept(@NotNull Long userId, @NotNull Long deptId) {
        try {
            int deletedCount = deptUserRepository.deleteByUserIdAndDeptId(userId, deptId);

            log.info("移除用户部门关联: userId={}, deptId={}, deletedCount={}, operator={}",
                    userId, deptId, deletedCount, getCurrentUserInfo());

        } catch (Exception ex) {
            log.error("移除用户部门关联失败: userId={}, deptId={}, operator={}",
                    userId, deptId, getCurrentUserInfo(), ex);
            throw AppErrors.CommonError.SERVER_ERROR.toException();
        }
    }

    @Transactional
    public void removeUserFromAllDepts(@NotNull Long userId) {
        try {
            int deletedCount = deptUserRepository.deleteByUserId(userId);

            log.info("移除用户所有部门关联: userId={}, deletedCount={}, operator={}",
                    userId, deletedCount, getCurrentUserInfo());

        } catch (Exception ex) {
            log.error("移除用户所有部门关联失败: userId={}, operator={}",
                    userId, getCurrentUserInfo(), ex);
            throw AppErrors.CommonError.SERVER_ERROR.toException();
        }
    }

    public List<SystemUser> getDeptUsers(@NotNull Long deptId, @Nullable Fetcher<SystemUser> fetcher) {
        List<SystemDeptUser> deptUsers = deptUserRepository.findByDeptId(deptId, null);
        List<Long> userIds = deptUsers.stream().map(SystemDeptUser::userId).toList();

        if (userIds.isEmpty()) {
            return List.of();
        }

        return userRepository.findByIds(userIds, fetcher);
    }

    public List<SystemDept> getUserDepts(@NotNull Long userId, @Nullable Fetcher<SystemDept> fetcher) {
        List<SystemDeptUser> deptUsers = deptUserRepository.findByUserId(userId, null);
        List<Long> deptIds = deptUsers.stream().map(SystemDeptUser::deptId).toList();

        if (deptIds.isEmpty()) {
            return List.of();
        }

        return deptRepository.findByIds(deptIds, fetcher);
    }

    public Optional<SystemDept> getUserPrimaryDept(@NotNull Long userId) {
        List<SystemDept> userDepts = getUserDepts(userId, null);
        return userDepts.isEmpty() ? Optional.empty() : Optional.of(userDepts.get(0));
    }

    private String buildAncestors(Long parentId) {
        if (parentId == null || parentId == 0) {
            return "0";
        }

        Optional<SystemDept> parentDept = findById(parentId, null);
        if (parentDept.isEmpty()) {
            return "0";
        }

        String parentAncestors = parentDept.get().ancestors();
        if (parentAncestors == null || parentAncestors.isEmpty() || parentAncestors.equals("0")) {
            return "0," + parentId;
        } else {
            return parentAncestors + "," + parentId;
        }
    }

    private boolean isDescendantOf(Long ancestorId, Long descendantId) {
        if (ancestorId.equals(descendantId)) {
            return true;
        }

        List<SystemDept> descendants = getDeptDescendants(descendantId);
        return descendants.stream().anyMatch(dept -> dept.id() == ancestorId);
    }

    private void updateChildDeptAncestors(Long deptId) {
        List<SystemDept> childDepts = findByParentId(deptId, null);
        for (SystemDept childDept : childDepts) {
            String newAncestors = buildAncestors(deptId);
            SystemDept updatedChild = Immutables.createSystemDept(draft -> {
                draft.setId(childDept.id());
                draft.setAncestors(newAncestors);
            });
            deptRepository.save(updatedChild);

            // 递归更新子部门的子部门
            updateChildDeptAncestors(childDept.id());
        }
    }

    private String getCurrentUserInfo() {
        try {
            return AuthContext.currentUser().toStringInfo();
        } catch (Exception e) {
            return "系统";
        }
    }
}
