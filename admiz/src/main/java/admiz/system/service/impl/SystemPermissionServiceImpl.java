package admiz.system.service.impl;

import admiz.auth.AuthContext;
import admiz.system.model.SystemResource;
import admiz.system.model.SystemRole;
import admiz.system.model.SystemUser;
import admiz.system.repository.SystemResourceRepository;
import admiz.system.repository.SystemRoleRepository;
import admiz.system.repository.SystemUserRepository;
import admiz.system.service.SystemPermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Service
public class SystemPermissionServiceImpl implements SystemPermissionService {

    private final SystemResourceRepository resourceRepository;
    private final SystemRoleRepository roleRepository;
    private final SystemUserRepository userRepository;

    @Override
    public boolean hasPermission(@NotNull Long userId, @NotNull String permission) {
        try {
            return resourceRepository.hasPermission(userId, permission);
        } catch (Exception e) {
            log.error("检查用户权限失败: userId={}, permission={}", userId, permission, e);
            return false;
        }
    }

    @Override
    public boolean hasAnyPermission(@NotNull Long userId, @NotNull List<String> permissions) {

        if (permissions.isEmpty()) {
            return false;
        }

        return permissions.stream().anyMatch(permission -> hasPermission(userId, permission));
    }

    @Override
    public boolean hasAllPermissions(@NotNull Long userId, @NotNull List<String> permissions) {
        if (permissions.isEmpty()) {
            return true;
        }

        return permissions.stream().allMatch(permission -> hasPermission(userId, permission));
    }

    @Override
    public boolean hasRole(@NotNull Long userId, @NotNull String roleName) {
        try {
            return userRepository.hasRole(userId, roleName);
        } catch (Exception e) {
            log.error("检查用户角色失败: userId={}, roleName={}", userId, roleName, e);
            return false;
        }
    }

    /**
     * 检查用户是否拥有任意一个角色
     */
    @Override
    public boolean hasAnyRole(@NotNull Long userId, @NotNull List<String> roleNames) {
        if (roleNames.isEmpty()) {
            return false;
        }
        
        try {
            return userRepository.hasAnyRole(userId, roleNames);
        } catch (Exception e) {
            log.error("检查用户角色失败: userId={}, roleNames={}", userId, roleNames, e);
            return false;
        }
    }

    /**
     * 检查用户是否拥有所有角色
     */
    @Override
    public boolean hasAllRoles(@NotNull Long userId, @NotNull List<String> roleNames) {
        if (roleNames.isEmpty()) {
            return true;
        }
        
        return roleNames.stream().allMatch(roleName -> hasRole(userId, roleName));
    }

    /**
     * 获取当前用户的所有权限
     */
    @Override
    public List<SystemResource> getCurrentUserPermissions() {
        Long currentUserId = getCurrentUserId();
        if (currentUserId == null) {
            return List.of();
        }
        return getUserPermissions(currentUserId);
    }

    /**
     * 获取当前用户的所有角色
     */
    @Override
    public List<SystemRole> getCurrentUserRoles() {
        Long currentUserId = getCurrentUserId();
        if (currentUserId == null) {
            return List.of();
        }
        return getUserRoles(currentUserId);
    }

    /**
     * 获取用户的所有权限
     */
    @Override
    public List<SystemResource> getUserPermissions(@NotNull Long userId) {
        try {
            return resourceRepository.findUserResources(userId, null);
        } catch (Exception e) {
            log.error("获取用户权限失败: userId={}", userId, e);
            return List.of();
        }
    }

    /**
     * 获取用户的所有角色
     */
    @Override
    public List<SystemRole> getUserRoles(@NotNull Long userId) {
        try {
            return roleRepository.findUserRoles(userId);
        } catch (Exception e) {
            log.error("获取用户角色失败: userId={}", userId, e);
            return List.of();
        }
    }

    /**
     * 获取用户的菜单权限
     */
    @Override
    public List<SystemResource> getUserMenuPermissions(@NotNull Long userId) {
        try {
            return resourceRepository.findUserMenuResources(userId, null);
        } catch (Exception e) {
            log.error("获取用户菜单权限失败: userId={}", userId, e);
            return List.of();
        }
    }

    /**
     * 获取当前用户的菜单权限
     */
    @Override
    public List<SystemResource> getCurrentUserMenuPermissions() {
        Long currentUserId = getCurrentUserId();
        if (currentUserId == null) {
            return List.of();
        }
        return getUserMenuPermissions(currentUserId);
    }

    /**
     * 检查当前用户是否拥有指定权限
     */
    @Override
    public boolean currentUserHasPermission(@NotNull String permission) {
        Long currentUserId = getCurrentUserId();
        if (currentUserId == null) {
            return false;
        }
        return hasPermission(currentUserId, permission);
    }

    /**
     * 检查当前用户是否拥有任意一个权限
     */
    @Override
    public boolean currentUserHasAnyPermission(@NotNull List<String> permissions) {
        Long currentUserId = getCurrentUserId();
        if (currentUserId == null) {
            return false;
        }
        return hasAnyPermission(currentUserId, permissions);
    }

    /**
     * 检查当前用户是否拥有所有权限
     */
    @Override
    public boolean currentUserHasAllPermissions(@NotNull List<String> permissions) {
        Long currentUserId = getCurrentUserId();
        if (currentUserId == null) {
            return false;
        }
        return hasAllPermissions(currentUserId, permissions);
    }

    /**
     * 检查当前用户是否拥有指定角色
     */
    @Override
    public boolean currentUserHasRole(@NotNull String roleName) {
        Long currentUserId = getCurrentUserId();
        if (currentUserId == null) {
            return false;
        }
        return hasRole(currentUserId, roleName);
    }

    /**
     * 检查当前用户是否拥有任意一个角色
     */
    @Override
    public boolean currentUserHasAnyRole(@NotNull List<String> roleNames) {
        Long currentUserId = getCurrentUserId();
        if (currentUserId == null) {
            return false;
        }
        return hasAnyRole(currentUserId, roleNames);
    }

    /**
     * 检查当前用户是否拥有所有角色
     */
    @Override
    public boolean currentUserHasAllRoles(@NotNull List<String> roleNames) {
        Long currentUserId = getCurrentUserId();
        if (currentUserId == null) {
            return false;
        }
        return hasAllRoles(currentUserId, roleNames);
    }

    /**
     * 获取当前登录用户ID
     */
    @Override
    @Nullable
    public Long getCurrentUserId() {
        try {
            SystemUser currentUser = AuthContext.currentUser();
            return currentUser != null ? currentUser.id() : null;
        } catch (Exception e) {
            log.debug("获取当前用户ID失败", e);
            return null;
        }
    }

    /**
     * 获取当前登录用户的登录名
     */
    @Override
    @Nullable
    public String getCurrentUserLogin() {
        try {
            SystemUser currentUser = AuthContext.currentUser();
            return currentUser != null ? currentUser.login() : null;
        } catch (Exception e) {
            log.debug("获取当前用户登录名失败", e);
            return null;
        }
    }

    /**
     * 检查是否已登录
     */
    @Override
    public boolean isAuthenticated() {
        return getCurrentUserId() != null;
    }

    /**
     * 检查是否是超级管理员
     */
    @Override
    public boolean isSuperAdmin(@NotNull Long userId) {
        try {
            SystemUser user = userRepository.findById(userId).orElse(null);
            return user != null && user.isBuiltinAdmin();
        } catch (Exception e) {
            log.error("检查超级管理员失败: userId={}", userId, e);
            return false;
        }
    }

    /**
     * 检查当前用户是否是超级管理员
     */
    @Override
    public boolean currentUserIsSuperAdmin() {
        Long currentUserId = getCurrentUserId();
        if (currentUserId == null) {
            return false;
        }
        return isSuperAdmin(currentUserId);
    }
}
