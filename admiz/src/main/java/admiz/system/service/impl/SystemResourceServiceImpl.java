package admiz.system.service.impl;

import admiz.auth.AuthContext;
import admiz.common.errorhandling.AppErrors;
import admiz.common.errorhandling.ErrorDetailException;
import admiz.system.model.Immutables;
import admiz.system.model.SystemResource;
import admiz.system.model.dto.CreateSystemResourceInput;
import admiz.system.model.dto.UpdateSystemResourceInput;
import admiz.system.repository.SystemResourceRepository;
import admiz.system.service.SystemResourceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.babyfish.jimmer.sql.ast.mutation.SaveMode;
import org.babyfish.jimmer.sql.fetcher.Fetcher;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Slf4j
@RequiredArgsConstructor
@Service
public class SystemResourceServiceImpl implements SystemResourceService {

    private final SystemResourceRepository resourceRepository;

    @Override
    @Transactional
    public SystemResource createResource(@NotNull CreateSystemResourceInput input) {
        if (existsByPermission(input.getPermission())) {
            throw AppErrors.CommonError.CONFLICT.toException("权限编码已存在: " + input.getPermission());
        }

        if (input.getParentId() != null && !resourceRepository.existsById(input.getParentId())) {
            throw AppErrors.CommonError.NOT_FOUND.toException("父级资源不存在: " + input.getParentId());
        }
        
        SystemResource resource = Immutables.createSystemResource(draft -> {
            draft.setName(input.getName());
            draft.setDisplayName(input.getDisplayName());
            draft.setParentId(input.getParentId());
            draft.setSortNum(input.getSortNum() != null ? input.getSortNum() : 0);
            draft.setPermission(input.getPermission());
            draft.setUrl(input.getUrl() != null ? input.getUrl() : "");
            draft.setTarget(input.getTarget() != null ? input.getTarget() : "");
            draft.setResourceType(input.getResourceType());
            draft.setDisabled(input.getDisabled() != null ? input.getDisabled() : false);
            draft.setHidden(input.getHidden() != null ? input.getHidden() : false);
            draft.setAutoRefresh(input.getAutoRefresh() != null ? input.getAutoRefresh() : false);
            draft.setIcon(input.getIcon() != null ? input.getIcon() : "");
            draft.setRemark(input.getRemark() != null ? input.getRemark() : "");
        });
        
        SystemResource savedResource = resourceRepository.save(resource);
        log.info("资源创建成功 - id: {}, name: {}, permission: {}, operator: {}",
                savedResource.id(), savedResource.name(), savedResource.permission(), getCurrentUserInfo());

        return savedResource;
    }

    /**
     * 更新资源
     */
    @Override
    @Transactional
    public SystemResource updateResource(@NotNull UpdateSystemResourceInput input) {
        log.debug("更新资源开始 - id: {}, name: {}, permission: {}",
                input.getId(), input.getName(), input.getPermission());
        
        // 检查资源是否存在
        if (!resourceRepository.existsById(input.getId())) {
            throw AppErrors.CommonError.NOT_FOUND.toException("资源不存在: " + input.getId());
        }
        
        // 检查权限编码是否被其他资源使用
        if (existsByPermissionAndIdNot(input.getPermission(), input.getId())) {
            throw AppErrors.CommonError.CONFLICT.toException("权限编码已被其他资源使用: " + input.getPermission());
        }
        
        // 如果指定了父级ID，检查父级是否存在且不是自己
        if (input.getParentId() != null) {
            if (input.getParentId().equals(input.getId())) {
                throw AppErrors.CommonError.FORBIDDEN.toException("不能将自己设置为父级");
            }
            if (!resourceRepository.existsById(input.getParentId())) {
                throw AppErrors.CommonError.NOT_FOUND.toException("父级资源不存在: " + input.getParentId());
            }
        }
        
        SystemResource resource = Immutables.createSystemResource(draft -> {
            draft.setId(input.getId());
            draft.setName(input.getName());
            draft.setDisplayName(input.getDisplayName());
            draft.setParentId(input.getParentId());
            draft.setSortNum(input.getSortNum() != null ? input.getSortNum() : 0);
            draft.setPermission(input.getPermission());
            draft.setUrl(input.getUrl() != null ? input.getUrl() : "");
            draft.setTarget(input.getTarget() != null ? input.getTarget() : "");
            draft.setResourceType(input.getResourceType());
            draft.setDisabled(input.getDisabled() != null ? input.getDisabled() : false);
            draft.setHidden(input.getHidden() != null ? input.getHidden() : false);
            draft.setAutoRefresh(input.getAutoRefresh() != null ? input.getAutoRefresh() : false);
            draft.setIcon(input.getIcon() != null ? input.getIcon() : "");
            draft.setRemark(input.getRemark() != null ? input.getRemark() : "");
        });
        
        SystemResource savedResource = resourceRepository.save(resource, SaveMode.UPDATE_ONLY);
        log.info("资源更新成功 - id: {}, name: {}, permission: {}, operator: {}",
                savedResource.id(), savedResource.name(), savedResource.permission(), getCurrentUserInfo());

        return savedResource;
    }

    /**
     * 删除资源
     */
    @Override
    @Transactional
    public boolean deleteResource(@NotNull Long resourceId) {
        log.debug("删除资源开始 - id: {}", resourceId);
        
        // 检查资源是否存在
        if (!resourceRepository.existsById(resourceId)) {
            log.warn("要删除的资源不存在 - id: {}", resourceId);
            return false;
        }
        
        // 检查是否有子资源
        List<SystemResource> children = findByParentId(resourceId, null);
        if (!children.isEmpty()) {
            throw AppErrors.CommonError.FORBIDDEN.toException("存在子资源，无法删除");
        }

        // 检查是否为内置资源
        if (resourceRepository.isBuiltinResource(resourceId)) {
            throw AppErrors.CommonError.FORBIDDEN.toException("内置资源无法删除");
        }
        
        try {
            resourceRepository.deleteById(resourceId);
            log.info("资源删除成功 - id: {}, operator: {}", resourceId, getCurrentUserInfo());
            return true;
        } catch (ErrorDetailException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除资源失败 - id: {}, operator: {}", resourceId, getCurrentUserInfo(), e);
            throw AppErrors.CommonError.INTERNAL_SERVER_ERROR.toException("删除资源失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询资源
     */
    @Override
    public Optional<SystemResource> findById(@NotNull Long resourceId, @Nullable Fetcher<SystemResource> fetcher) {
        return resourceRepository.findById(resourceId, fetcher);
    }

    /**
     * 根据ID查询资源（必须存在）
     */
    @Override
    public SystemResource getById(@NotNull Long resourceId, @Nullable Fetcher<SystemResource> fetcher) {
        return findById(resourceId, fetcher)
                .orElseThrow(() -> AppErrors.CommonError.NOT_FOUND.toException("资源不存在: " + resourceId));
    }

    /**
     * 根据权限编码查询资源
     */
    @Override
    public List<SystemResource> findByPermission(@NotNull String permission) {
        return resourceRepository.findByPermission(permission);
    }

    /**
     * 分页查询资源列表
     */
    @Override
    public Page<SystemResource> paginate(@Nullable String searchText,
                                        @Nullable SystemResource.ResourceType resourceType,
                                        @Nullable Boolean disabled,
                                        @NotNull Pageable pageable,
                                        @Nullable Fetcher<SystemResource> fetcher) {
        return resourceRepository.paginate(searchText, resourceType, disabled, pageable, fetcher);
    }

    /**
     * 查询所有可用资源
     */
    @Override
    public List<SystemResource> findAllAvailable(@Nullable Fetcher<SystemResource> fetcher) {
        return resourceRepository.findAllAvailable(fetcher);
    }

    /**
     * 构建资源树结构
     */
    @Override
    public List<SystemResource> buildResourceTree(@Nullable Fetcher<SystemResource> fetcher) {
        // 获取所有资源
        List<SystemResource> allResources = resourceRepository.findResourceTree(fetcher);

        // 构建树状结构
        return buildTreeFromList(allResources);
    }

    /**
     * 从平铺列表构建树状结构
     */
    private List<SystemResource> buildTreeFromList(List<SystemResource> allResources) {
        Map<Long, List<SystemResource>> parentChildMap = new HashMap<>();
        List<SystemResource> rootResources = new ArrayList<>();

        // 按父级ID分组
        for (SystemResource resource : allResources) {
            if (resource.parentId() == null) {
                rootResources.add(resource);
            } else {
                parentChildMap.computeIfAbsent(resource.parentId(), k -> new ArrayList<>()).add(resource);
            }
        }

        return rootResources;
    }

    /**
     * 根据父级ID查询子资源
     */
    @Override
    public List<SystemResource> findByParentId(@Nullable Long parentId, @Nullable Fetcher<SystemResource> fetcher) {
        return resourceRepository.findByParentId(parentId, fetcher);
    }

    /**
     * 查询菜单类型的资源
     */
    @Override
    public List<SystemResource> findMenuResources(@Nullable Fetcher<SystemResource> fetcher) {
        return resourceRepository.findMenuResources(fetcher);
    }

    /**
     * 根据资源类型查询资源
     */
    @Override
    public List<SystemResource> findByResourceType(@NotNull SystemResource.ResourceType resourceType, 
                                                  @Nullable Fetcher<SystemResource> fetcher) {
        return resourceRepository.findByResourceType(resourceType, fetcher);
    }

    /**
     * 检查权限编码是否存在
     */
    @Override
    public boolean existsByPermission(@NotNull String permission) {
        return !resourceRepository.findByPermission(permission).isEmpty();
    }

    /**
     * 检查权限编码是否存在（排除指定ID）
     */
    @Override
    public boolean existsByPermissionAndIdNot(@NotNull String permission, @Nullable Long excludeId) {
        List<SystemResource> resources = resourceRepository.findByPermission(permission);
        if (excludeId == null) {
            return !resources.isEmpty();
        }
        return resources.stream().anyMatch(resource -> !excludeId.equals(resource.id()));
    }

    /**
     * 根据用户ID查询用户拥有的资源权限
     */
    @Override
    public List<SystemResource> findUserResources(@NotNull Long userId, @Nullable Fetcher<SystemResource> fetcher) {
        return resourceRepository.findUserResources(userId, fetcher);
    }

    /**
     * 根据用户ID查询菜单类型的资源
     */
    @Override
    public List<SystemResource> findUserMenuResources(@NotNull Long userId, @Nullable Fetcher<SystemResource> fetcher) {
        return resourceRepository.findUserMenuResources(userId, fetcher);
    }

    /**
     * 检查用户是否拥有指定权限
     */
    @Override
    public boolean hasPermission(@NotNull Long userId, @NotNull String permission) {
        return resourceRepository.hasPermission(userId, permission);
    }

    /**
     * 检查用户是否拥有任意一个权限
     */
    @Override
    public boolean hasAnyPermission(@NotNull Long userId, @NotNull List<String> permissions) {

        if (permissions.isEmpty()) {
            return false;
        }
        
        return permissions.stream().anyMatch(permission -> hasPermission(userId, permission));
    }

    /**
     * 检查用户是否拥有所有权限
     */
    @Override
    public boolean hasAllPermissions(@NotNull Long userId, @NotNull List<String> permissions) {
        if (permissions.isEmpty()) {
            return true;
        }
        
        return permissions.stream().allMatch(permission -> hasPermission(userId, permission));
    }



    private String getCurrentUserInfo() {
        try {
            return AuthContext.currentUser().toStringInfo();
        } catch (Exception e) {
            return "系统";
        }
    }
}
