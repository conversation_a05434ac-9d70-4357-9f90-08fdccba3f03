package admiz.system.service.impl;

import admiz.auth.AuthContext;
import admiz.common.errorhandling.AppErrors;
import admiz.common.errorhandling.ErrorDetailException;
import admiz.system.model.Immutables;
import admiz.system.model.SystemResource;
import admiz.system.model.SystemRole;
import admiz.system.model.dto.BatchOperationResult;
import admiz.system.model.dto.CreateSystemRoleInput;
import admiz.system.model.dto.UpdateSystemRoleInput;
import admiz.system.repository.SystemResourceRepository;
import admiz.system.repository.SystemRoleRepository;
import admiz.system.service.SystemPermissionAuditService;
import admiz.system.service.SystemRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.babyfish.jimmer.sql.ast.mutation.SaveMode;
import org.babyfish.jimmer.sql.fetcher.Fetcher;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 系统角色管理服务
 * 
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SystemRoleServiceimpl implements SystemRoleService {
    
    private final SystemRoleRepository roleRepository;
    private final SystemResourceRepository resourceRepository;
    private final SystemPermissionAuditService auditService;

    /**
     * 创建角色
     * 
     * @param input 角色创建输入
     * @return 创建的角色
     */
    @Transactional
    public SystemRole createRole(@NotNull CreateSystemRoleInput input) {
        try {
            // 创建角色对象
            SystemRole role = Immutables.createSystemRole(draft -> {
                draft.setName(input.getName());
                draft.setDisplayName(input.getDisplayName());
                draft.setSortNum(input.getSortNum() != null ? input.getSortNum() : 0);
                draft.setRemark(input.getRemark());
                draft.setDisabled(input.getDisabled() != null ? input.getDisabled() : false);
                draft.setDeleted(false);
            });

            SystemRole savedRole = roleRepository.save(role, SaveMode.INSERT_ONLY);

            // 记录审计日志
            try {
                Long operatorUserId = AuthContext.currentUser().id();
                String operatorUserLogin = AuthContext.currentUser().login();
                auditService.logRoleCreate(operatorUserId, operatorUserLogin,
                        savedRole.id(), savedRole.name(), true, null);
            } catch (Exception auditEx) {
                log.warn("记录角色创建审计日志失败", auditEx);
            }

            log.info("创建角色成功: roleId={}, name={}, operator={}",
                    savedRole.id(), savedRole.name(), getCurrentUserInfo());

            return savedRole;
        } catch (Exception ex) {
            // 记录失败的审计日志
            try {
                Long operatorUserId = AuthContext.currentUser().id();
                String operatorUserLogin = AuthContext.currentUser().login();
                auditService.logRoleCreate(operatorUserId, operatorUserLogin,
                        null, input.getName(), false, ex.getMessage());
            } catch (Exception auditEx) {
                log.warn("记录角色创建失败审计日志失败", auditEx);
            }

            log.error("创建角色失败: name={}, operator={}",
                    input.getName(), getCurrentUserInfo(), ex);
            throw AppErrors.CommonError.SERVER_ERROR.toException();
        }
    }

    /**
     * 更新角色
     *
     * @param input 角色更新输入
     * @return 更新的角色
     */
    @Transactional
    public SystemRole updateRole(@NotNull UpdateSystemRoleInput input) {
        try {

            SystemRole existingRole = roleRepository.findById(input.getId())
                    .orElseThrow(() -> AppErrors.CommonError.NOT_FOUND.toException("角色不存在: " + input.getId()));

            // 管理员保护逻辑
            if (existingRole.name().equals("ROLE_ADMIN")) {
                if (!input.getName().equals("ROLE_ADMIN")) {
                    throw AppErrors.CommonError.FORBIDDEN.toException("不能修改系统内置管理员角色的名称");
                }
                if (input.getDisabled() != null && input.getDisabled()) {
                    throw AppErrors.CommonError.FORBIDDEN.toException("不能禁用系统内置管理员角色");
                }
            }

            // 名称唯一性检查
            if (!existingRole.name().equals(input.getName()) && 
                existsByNameAndIdNot(input.getName(), input.getId())) {
                throw AppErrors.CommonError.INVALID_PARAMETER.toException("角色名称已存在: " + input.getName());
            }

            // 更新角色对象
            SystemRole role = Immutables.createSystemRole(draft -> {
                draft.setId(input.getId());
                draft.setName(input.getName());
                draft.setDisplayName(input.getDisplayName());
                if (input.getSortNum() != null) {
                    draft.setSortNum(input.getSortNum());
                }
                if (input.getRemark() != null) {
                    draft.setRemark(input.getRemark());
                }
                if (input.getDisabled() != null) {
                    draft.setDisabled(input.getDisabled());
                }
            });

            SystemRole updatedRole = roleRepository.save(role, SaveMode.UPDATE_ONLY);
            
            log.info("更新角色成功: roleId={}, name={}, operator={}", 
                    updatedRole.id(), updatedRole.name(), getCurrentUserInfo());
            
            return updatedRole;
        } catch (ErrorDetailException e) {
            throw e;
        } catch (Exception ex) {
            log.error("更新角色失败: roleId={}, name={}, operator={}", 
                    input.getId(), input.getName(), getCurrentUserInfo(), ex);
            throw AppErrors.CommonError.SERVER_ERROR.toException();
        }
    }

    /**
     * 删除角色
     * 
     * @param roleId 角色ID
     * @return 是否删除成功
     */
    @Transactional
    public boolean deleteRole(@NotNull Long roleId) {
        try {
            // 检查角色是否存在
            SystemRole role = roleRepository.findById(roleId)
                    .orElseThrow(() -> AppErrors.CommonError.NOT_FOUND.toException("角色不存在: " + roleId));

            // 检查是否为管理员角色
            if (role.name().equals("ROLE_ADMIN")) {
                throw AppErrors.CommonError.FORBIDDEN.toException("不能删除系统内置管理员角色");
            }

            // 检查角色是否被用户使用
            if (roleRepository.isRoleInUse(roleId)) {
                throw AppErrors.CommonError.FORBIDDEN.toException("角色正在被用户使用，无法删除");
            }

            // 软删除角色
            boolean deleted = roleRepository.softDelete(roleId);
            
            if (deleted) {
                log.info("删除角色成功: roleId={}, name={}, operator={}", 
                        roleId, role.name(), getCurrentUserInfo());
            }
            
            return deleted;
        } catch (ErrorDetailException e) {
            throw e;
        } catch (Exception ex) {
            log.error("删除角色失败: roleId={}, operator={}", 
                    roleId, getCurrentUserInfo(), ex);
            throw AppErrors.CommonError.SERVER_ERROR.toException();
        }
    }

    /**
     * 根据ID查询角色
     * 
     * @param roleId 角色ID
     * @param fetcher 查询字段配置
     * @return 角色信息
     */
    public Optional<SystemRole> findById(@NotNull Long roleId, @Nullable Fetcher<SystemRole> fetcher) {
        return roleRepository.findById(roleId, fetcher);
    }

    /**
     * 根据ID查询角色（必须存在）
     * 
     * @param roleId 角色ID
     * @param fetcher 查询字段配置
     * @return 角色信息
     */
    public SystemRole getById(@NotNull Long roleId, @Nullable Fetcher<SystemRole> fetcher) {
        return findById(roleId, fetcher)
                .orElseThrow(() -> AppErrors.CommonError.NOT_FOUND.toException("角色不存在: " + roleId));
    }

    /**
     * 根据名称查询角色
     *
     * @param name 角色名称
     * @return 角色信息
     */
    public Optional<SystemRole> findByName(@NotNull String name) {
        return roleRepository.findByName(name, null);
    }

    /**
     * 检查角色名称是否存在
     *
     * @param name 角色名称
     * @return 是否存在
     */
    public boolean existsByName(@NotNull String name) {
        return roleRepository.existsByName(name);
    }

    /**
     * 分页查询角色列表
     *
     * @param searchText 搜索关键词
     * @param disabled 是否禁用
     * @param pageable 分页参数
     * @param fetcher 查询字段配置
     * @return 分页角色列表
     */
    public Page<SystemRole> paginate(@Nullable String searchText,
                                   @Nullable Boolean disabled,
                                   @NotNull Pageable pageable,
                                   @Nullable Fetcher<SystemRole> fetcher) {
        log.debug("SystemRoleService.paginate 开始 - searchText: '{}', disabled: {}, pageable: {}", 
              searchText, disabled, pageable);
        
        // 记录搜索文本的详细信息
        if (searchText != null) {
            log.debug("搜索文本详细信息 - 长度: {}, 是否空白: {}, 去除空白后: '{}'", 
                  searchText.length(), searchText.isBlank(), searchText.trim());
        }
        
        Page<SystemRole> result = roleRepository.paginate(searchText, disabled, pageable, fetcher);
        
        log.debug("SystemRoleService.paginate 结果 - 总数: {}, 当前页: {}, 每页大小: {}, 总页数: {}", 
              result.getTotalElements(), result.getNumber(), result.getSize(), result.getTotalPages());
        
        if (log.isDebugEnabled() && result.hasContent()) {
            log.debug("查询到的角色列表:");
            result.getContent().forEach(role -> 
                log.debug("  - id: {}, name: '{}', displayName: '{}', remark: '{}', disabled: {}", 
                      role.id(), role.name(), role.displayName(), 
                      role.remark() != null ? role.remark() : "null", role.disabled()));
        }
        
        return result;
    }

    /**
     * 查询所有可用角色
     *
     * @param fetcher 查询字段配置
     * @return 角色列表
     */
    public List<SystemRole> findAllAvailable(@Nullable Fetcher<SystemRole> fetcher) {
        return roleRepository.findAllAvailable(fetcher);
    }

    /**
     * 为角色分配权限
     * 
     * @param roleId 角色ID
     * @param resourceIds 权限资源ID列表
     * @return 是否分配成功
     */
    @Transactional
    public boolean assignPermissions(@NotNull Long roleId, @NotNull List<Long> resourceIds) {

        try {
            // 检查角色是否存在
            SystemRole role = getById(roleId, null);

            // 检查权限资源是否都存在
            List<SystemResource> resources = resourceRepository.findByIds(resourceIds);
            if (resources.size() != resourceIds.size()) {
                throw AppErrors.CommonError.INVALID_PARAMETER.toException("部分权限资源不存在");
            }

            // 为角色分配权限
            roleRepository.assignResources(roleId, resourceIds);

            // 记录审计日志
            try {
                Long operatorUserId = AuthContext.currentUser().id();
                String operatorUserLogin = AuthContext.currentUser().login();
                List<String> permissions = resources.stream()
                        .map(SystemResource::permission)
                        .toList();
                auditService.logRolePermissionAssign(operatorUserId, operatorUserLogin,
                        roleId, role.name(), resourceIds, permissions, true, null);
            } catch (Exception auditEx) {
                log.warn("记录角色权限分配审计日志失败", auditEx);
            }

            log.info("角色权限分配成功: roleId={}, resourceCount={}, operator={}",
                    roleId, resourceIds.size(), getCurrentUserInfo());

            return true;
        } catch (ErrorDetailException e) {
            // 记录失败的审计日志
            try {
                Long operatorUserId = AuthContext.currentUser().id();
                String operatorUserLogin = AuthContext.currentUser().login();
                SystemRole role = findById(roleId, null).orElse(null);
                String roleName = role != null ? role.name() : "未知角色";
                auditService.logRolePermissionAssign(operatorUserId, operatorUserLogin,
                        roleId, roleName, resourceIds, List.of(), false, e.getMessage());
            } catch (Exception auditEx) {
                log.warn("记录角色权限分配失败审计日志失败", auditEx);
            }
            throw e;
        } catch (Exception ex) {
            // 记录失败的审计日志
            try {
                Long operatorUserId = AuthContext.currentUser().id();
                String operatorUserLogin = AuthContext.currentUser().login();
                SystemRole role = findById(roleId, null).orElse(null);
                String roleName = role != null ? role.name() : "未知角色";
                auditService.logRolePermissionAssign(operatorUserId, operatorUserLogin,
                        roleId, roleName, resourceIds, List.of(), false, ex.getMessage());
            } catch (Exception auditEx) {
                log.warn("记录角色权限分配失败审计日志失败", auditEx);
            }

            log.error("角色权限分配失败: roleId={}, operator={}",
                    roleId, getCurrentUserInfo(), ex);
            throw AppErrors.CommonError.SERVER_ERROR.toException();
        }
    }

    /**
     * 获取角色的权限列表
     * 
     * @param roleId 角色ID
     * @return 权限资源列表
     */
    public List<SystemResource> getRolePermissions(@NotNull Long roleId) {
        SystemRole role = getById(roleId, null);
        return resourceRepository.findResourceForRoles(List.of(role), null);
    }

    @Override
    @Transactional
    public BatchOperationResult batchUpdateRoleStatus(@NotNull List<Long> roleIds, @NotNull Boolean disabled) {
        int successCount = 0;
        int failCount = 0;
        List<String> errorMessages = new ArrayList<>();

        for (Long roleId : roleIds) {
            try {
                // 获取现有角色信息
                SystemRole existingRole = getById(roleId, null);

                // 创建更新输入对象
                UpdateSystemRoleInput updateInput = new UpdateSystemRoleInput();
                updateInput.setId(roleId);
                updateInput.setDisabled(disabled);
                updateInput.setName(existingRole.name());
                updateInput.setDisplayName(existingRole.displayName());
                updateInput.setSortNum(existingRole.sortNum());
                updateInput.setRemark(existingRole.remark());

                updateRole(updateInput);
                successCount++;

            } catch (ErrorDetailException e) {
                failCount++;
                errorMessages.add(String.format("角色ID %d: %s", roleId, e.getMessage()));
                log.warn("批量更新角色状态失败: roleId={}, disabled={}, error={}",
                        roleId, disabled, e.getMessage());
            } catch (Exception e) {
                failCount++;
                errorMessages.add(String.format("角色ID %d: 系统错误", roleId));
                log.warn("批量更新角色状态失败: roleId={}, disabled={}", roleId, disabled, e);
            }
        }

        log.info("批量更新角色状态完成: 成功={}, 失败={}, operator={}",
                successCount, failCount, getCurrentUserInfo());

        return new BatchOperationResult(successCount, failCount, errorMessages);
    }

    /**
     * 获取当前用户信息
     */
    private String getCurrentUserInfo() {
        try {
            return AuthContext.currentUser().toStringInfo();
        } catch (Exception e) {
            return "系统";
        }
    }

    /**
     * 检查角色名称是否存在（排除指定ID）
     *
     * @param name 角色名称
     * @param excludeId 排除的角色ID
     * @return 是否存在
     */
    public boolean existsByNameAndIdNot(@NotNull String name, @Nullable Long excludeId) {
        return roleRepository.existsByNameAndIdNot(name, excludeId);
    }
}