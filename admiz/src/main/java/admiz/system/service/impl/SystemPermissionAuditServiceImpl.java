package admiz.system.service.impl;

import admiz.system.model.SystemPermissionLog;
import admiz.system.model.SystemPermissionLogDraft;
import admiz.system.repository.SystemPermissionLogRepository;
import admiz.system.service.SystemPermissionAuditService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.babyfish.jimmer.sql.ast.mutation.SaveMode;
import org.babyfish.jimmer.sql.ast.tuple.Tuple2;
import org.babyfish.jimmer.sql.ast.tuple.Tuple3;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 系统权限审计服务实现
 * */
@Slf4j
@Service
@RequiredArgsConstructor
public class SystemPermissionAuditServiceImpl implements SystemPermissionAuditService {

    private final SystemPermissionLogRepository permissionLogRepository;
    private final ObjectMapper objectMapper;


    @Override
    @Async
    public void logUserLogin(@NotNull Long userId, @NotNull String userLogin,
                            @Nullable String clientIp, @Nullable String userAgent,
                            @Nullable String sessionId, boolean success, @Nullable String errorMessage) {
        try {
            Map<String, Object> details = new HashMap<>();
            details.put("clientIp", clientIp);
            details.put("userAgent", userAgent);
            details.put("sessionId", sessionId);

            SystemPermissionLog auditLog = createLog(
                    SystemPermissionLog.OperationType.USER_LOGIN,
                    success ? SystemPermissionLog.OperationResult.SUCCESS : SystemPermissionLog.OperationResult.FAILED,
                    SystemPermissionLog.RecordStatus.RECORDED,
                    userId, userLogin, null, null, null, null, null, null,
                    success ? "用户登录成功" : "用户登录失败",
                    details, clientIp, userAgent, sessionId, null, errorMessage, null
            );

            permissionLogRepository.sql().saveCommand(auditLog)
                    .setMode(SaveMode.INSERT_ONLY)
                    .execute();
            log.debug("记录用户登录日志: 用户id={}, 用户名={}, success={}", userId, userLogin, success);
        } catch (Exception e) {
            log.error("记录用户登录日志失败: 用户id={}, 用户名={}", userId, userLogin, e);
        }
    }

    @Override
    @Async
    public void logUserLogout(@NotNull Long userId, @NotNull String userLogin, @Nullable String sessionId) {
        try {
            Map<String, Object> details = new HashMap<>();
            details.put("sessionId", sessionId);

            SystemPermissionLog auditLog = createLog(
                    SystemPermissionLog.OperationType.USER_LOGOUT,
                    SystemPermissionLog.OperationResult.SUCCESS,
                    SystemPermissionLog.RecordStatus.RECORDED,
                    userId, userLogin, null, null, null, null, null, null,
                    "用户登出",
                    details, null, null, sessionId, null, null, null
            );

            permissionLogRepository.sql().saveCommand(auditLog)
                    .setMode(SaveMode.INSERT_ONLY)
                    .execute();
            log.debug("记录用户登出日志: 用户id={}, 用户名={}", userId, userLogin);
        } catch (Exception e) {
            log.error("记录用户登出日志失败: 用户id={}, 用户名={}", userId, userLogin, e);
        }
    }

    @Override
    @Async
    public void logUserRoleAssign(@NotNull Long operatorUserId, @NotNull String operatorUserLogin,
                                 @NotNull Long targetUserId, @NotNull String targetUserLogin,
                                 @NotNull List<Long> roleIds, @NotNull List<String> roleNames,
                                 boolean success, @Nullable String errorMessage) {
        try {
            Map<String, Object> details = new HashMap<>();
            details.put("roleIds", roleIds);
            details.put("roleNames", roleNames);

            SystemPermissionLog auditLog = createLog(
                    SystemPermissionLog.OperationType.USER_ROLE_ASSIGN,
                    success ? SystemPermissionLog.OperationResult.SUCCESS : SystemPermissionLog.OperationResult.FAILED,
                    SystemPermissionLog.RecordStatus.RECORDED,
                    operatorUserId, operatorUserLogin, targetUserId, targetUserLogin, null, null, null, null,
                    String.format("为用户%s 分配角色: %s", targetUserLogin, String.join(", ", roleNames)),
                    details, null, null, null, null, errorMessage, null
            );

            permissionLogRepository.sql().saveCommand(auditLog)
                    .setMode(SaveMode.INSERT_ONLY)
                    .execute();
            log.debug("记录用户角色分配日志: operatorUserId={}, targetUserId={}, success={}",
                     operatorUserId, targetUserId, success);
        } catch (Exception e) {
            log.error("记录用户角色分配日志失败: operatorUserId={}, targetUserId={}",
                     operatorUserId, targetUserId, e);
        }
    }

    @Override
    @Async
    public void logUserRoleRemove(@NotNull Long operatorUserId, @NotNull String operatorUserLogin,
                                 @NotNull Long targetUserId, @NotNull String targetUserLogin,
                                 @NotNull List<Long> roleIds, @NotNull List<String> roleNames,
                                 boolean success, @Nullable String errorMessage) {
        try {
            Map<String, Object> details = new HashMap<>();
            details.put("roleIds", roleIds);
            details.put("roleNames", roleNames);

            SystemPermissionLog auditLog = createLog(
                    SystemPermissionLog.OperationType.USER_ROLE_REMOVE,
                    success ? SystemPermissionLog.OperationResult.SUCCESS : SystemPermissionLog.OperationResult.FAILED,
                    SystemPermissionLog.RecordStatus.RECORDED,
                    operatorUserId, operatorUserLogin, targetUserId, targetUserLogin, null, null, null, null,
                    String.format("移除用户 %s 的角色: %s", targetUserLogin, String.join(", ", roleNames)),
                    details, null, null, null, null, errorMessage, null
            );

            permissionLogRepository.sql().saveCommand(auditLog)
                    .setMode(SaveMode.INSERT_ONLY)
                    .execute();
            log.debug("记录用户角色移除日志: operatorUserId={}, targetUserId={}, success={}",
                     operatorUserId, targetUserId, success);
        } catch (Exception e) {
            log.error("记录用户角色移除日志失败: operatorUserId={}, targetUserId={}",
                     operatorUserId, targetUserId, e);
        }
    }

    @Override
    @Async
    public void logUserManagement(@NotNull Long operatorUserId, @NotNull String operatorUserLogin,
                                  @NotNull Long targetUserId, @NotNull String targetUserLogin,
                                  @NotNull String operationType, @NotNull String operationDescription,
                                  boolean success, @Nullable String errorMessage) {
        try {
            Map<String, Object> details = new HashMap<>();
            details.put("operationType", operationType);
            details.put("success", success);

            SystemPermissionLog.OperationType opType;
            try {
                opType = SystemPermissionLog.OperationType.valueOf(operationType);
            } catch (IllegalArgumentException e) {
                opType = SystemPermissionLog.OperationType.USER_UPDATE; // 默认为用户更新
            }

            SystemPermissionLog auditLog = createLog(
                    opType,
                    success ? SystemPermissionLog.OperationResult.SUCCESS : SystemPermissionLog.OperationResult.FAILED,
                    SystemPermissionLog.RecordStatus.RECORDED,
                    operatorUserId, operatorUserLogin, targetUserId, targetUserLogin, null, null, null, null,
                    operationDescription,
                    details, null, null, null, null, errorMessage, null
            );

            permissionLogRepository.sql().saveCommand(auditLog)
                    .setMode(SaveMode.INSERT_ONLY)
                    .execute();
            log.debug("记录用户管理日志: operatorUserId={}, targetUserId={}, operationType={}, success={}",
                    operatorUserId, targetUserId, operationType, success);
        } catch (Exception e) {
            log.error("记录用户管理日志失败: operatorUserId={}, targetUserId={}, operationType={}",
                    operatorUserId, targetUserId, operationType, e);
        }
    }

    @Override
    @Async
    public void logRoleCreate(@NotNull Long operatorUserId, @NotNull String operatorUserLogin,
                             @NotNull Long roleId, @NotNull String roleName,
                             boolean success, @Nullable String errorMessage) {
        try {
            SystemPermissionLog auditLog = createLog(
                    SystemPermissionLog.OperationType.ROLE_CREATE,
                    success ? SystemPermissionLog.OperationResult.SUCCESS : SystemPermissionLog.OperationResult.FAILED,
                    SystemPermissionLog.RecordStatus.RECORDED,
                    operatorUserId, operatorUserLogin, null, null, roleId, roleName, null, null,
                    String.format("创建角色: %s", roleName),
                    null, null, null, null, null, errorMessage, null
            );

            permissionLogRepository.sql().saveCommand(auditLog)
                    .setMode(SaveMode.INSERT_ONLY)
                    .execute();
            log.debug("记录角色创建日志: operatorUserId={}, roleId={}, success={}", 
                     operatorUserId, roleId, success);
        } catch (Exception e) {
            log.error("记录角色创建日志失败: operatorUserId={}, roleId={}", operatorUserId, roleId, e);
        }
    }

    @Override
    @Async
    public void logRoleUpdate(@NotNull Long operatorUserId, @NotNull String operatorUserLogin,
                             @NotNull Long roleId, @NotNull String roleName,
                             @Nullable String changeDetails,
                             boolean success, @Nullable String errorMessage) {
        try {
            Map<String, Object> details = new HashMap<>();
            if (changeDetails != null) {
                details.put("changes", changeDetails);
            }

            SystemPermissionLog auditLog = createLog(
                    SystemPermissionLog.OperationType.ROLE_UPDATE,
                    success ? SystemPermissionLog.OperationResult.SUCCESS : SystemPermissionLog.OperationResult.FAILED,
                    SystemPermissionLog.RecordStatus.RECORDED,
                    operatorUserId, operatorUserLogin, null, null, roleId, roleName, null, null,
                    String.format("更新角色: %s", roleName),
                    details, null, null, null, null, errorMessage, null
            );

            permissionLogRepository.sql().saveCommand(auditLog)
                    .setMode(SaveMode.INSERT_ONLY)
                    .execute();
            log.debug("记录角色更新日志: operatorUserId={}, roleId={}, success={}",
                     operatorUserId, roleId, success);
        } catch (Exception e) {
            log.error("记录角色更新日志失败: operatorUserId={}, roleId={}", operatorUserId, roleId, e);
        }
    }

    @Override
    @Async
    public void logRoleDelete(@NotNull Long operatorUserId, @NotNull String operatorUserLogin,
                             @NotNull Long roleId, @NotNull String roleName,
                             boolean success, @Nullable String errorMessage) {
        try {
            SystemPermissionLog auditLog = createLog(
                    SystemPermissionLog.OperationType.ROLE_DELETE,
                    success ? SystemPermissionLog.OperationResult.SUCCESS : SystemPermissionLog.OperationResult.FAILED,
                    SystemPermissionLog.RecordStatus.RECORDED,
                    operatorUserId, operatorUserLogin, null, null, roleId, roleName, null, null,
                    String.format("删除角色: %s", roleName),
                    null, null, null, null, null, errorMessage, null
            );

            permissionLogRepository.sql().saveCommand(auditLog)
                    .setMode(SaveMode.INSERT_ONLY)
                    .execute();
            log.debug("记录角色删除日志: operatorUserId={}, roleId={}, success={}",
                     operatorUserId, roleId, success);
        } catch (Exception e) {
            log.error("记录角色删除日志失败: operatorUserId={}, roleId={}", operatorUserId, roleId, e);
        }
    }

    @Override
    @Async
    public void logRolePermissionAssign(@NotNull Long operatorUserId, @NotNull String operatorUserLogin,
                                       @NotNull Long roleId, @NotNull String roleName,
                                       @NotNull List<Long> resourceIds, @NotNull List<String> permissions,
                                       boolean success, @Nullable String errorMessage) {
        try {
            Map<String, Object> details = new HashMap<>();
            details.put("resourceIds", resourceIds);
            details.put("permissions", permissions);

            SystemPermissionLog auditLog = createLog(
                    SystemPermissionLog.OperationType.ROLE_PERMISSION_ASSIGN,
                    success ? SystemPermissionLog.OperationResult.SUCCESS : SystemPermissionLog.OperationResult.FAILED,
                    SystemPermissionLog.RecordStatus.RECORDED,
                    operatorUserId, operatorUserLogin, null, null, roleId, roleName, null, null,
                    String.format("为角色: %s 分配权限: %s", roleName, String.join(", ", permissions)),
                    details, null, null, null, null, errorMessage, null
            );

            permissionLogRepository.sql().saveCommand(auditLog)
                    .setMode(SaveMode.INSERT_ONLY)
                    .execute();
            log.debug("记录角色权限分配日志: operatorUserId={}, roleId={}, success={}", 
                     operatorUserId, roleId, success);
        } catch (Exception e) {
            log.error("记录角色权限分配日志失败: operatorUserId={}, roleId={}", operatorUserId, roleId, e);
        }
    }

    private SystemPermissionLog createLog(SystemPermissionLog.OperationType operationType,
                                         SystemPermissionLog.OperationResult operationResult,
                                          SystemPermissionLog.RecordStatus recordStatus,
                                         @Nullable Long operatorUserId, @Nullable String operatorUserLogin,
                                         @Nullable Long targetUserId, @Nullable String targetUserLogin,
                                         @Nullable Long targetRoleId, @Nullable String targetRoleName,
                                         @Nullable Long targetResourceId, @Nullable String targetPermission,
                                         String operationDescription,
                                         @Nullable Map<String, Object> operationDetails,
                                         @Nullable String clientIp, @Nullable String userAgent,
                                         @Nullable String sessionId, @Nullable Long operationDuration,
                                         @Nullable String errorMessage, @Nullable String remark) {
        return SystemPermissionLogDraft.$.produce(draft -> {
            draft.setOperationType(operationType);
            draft.setOperationResult(operationResult);
            draft.setRecordStatus(recordStatus);
            draft.setOperatorUserId(operatorUserId);
            draft.setOperatorUserLogin(operatorUserLogin);
            draft.setTargetUserId(targetUserId);
            draft.setTargetUserLogin(targetUserLogin);
            draft.setTargetRoleId(targetRoleId);
            draft.setTargetRoleName(targetRoleName);
            draft.setTargetResourceId(targetResourceId);
            draft.setTargetPermission(targetPermission);
            draft.setOperationDescription(operationDescription);
            draft.setOperationDetails(operationDetails != null ? toJsonString(operationDetails) : null);
            draft.setClientIp(clientIp);
            draft.setUserAgent(userAgent);
            draft.setSessionId(sessionId);
            draft.setOperationDuration(operationDuration);
            draft.setErrorMessage(errorMessage);
            draft.setRemark(remark);
        });
    }

    @Override
    @Async
    public void logPermissionCheck(@NotNull Long userId, @NotNull String userLogin,
                                  @NotNull String permission, boolean granted,
                                  @Nullable Long operationDuration) {
        try {
            Map<String, Object> details = new HashMap<>();
            details.put("permission", permission);
            details.put("granted", granted);

            SystemPermissionLog auditLog = createLog(
                    SystemPermissionLog.OperationType.PERMISSION_CHECK,
                    granted ? SystemPermissionLog.OperationResult.SUCCESS : SystemPermissionLog.OperationResult.DENIED,
                    SystemPermissionLog.RecordStatus.RECORDED,
                    userId, userLogin, null, null, null, null, null, permission,
                    String.format("权限检查 %s - %s", permission, granted ? "授予" : "拒绝"),
                    details, null, null, null, operationDuration, null, null
            );

            permissionLogRepository.sql().saveCommand(auditLog)
                    .setMode(SaveMode.INSERT_ONLY)
                    .execute();
            log.debug("记录权限检查日志 userId={}, permission={}, granted={}", userId, permission, granted);
        } catch (Exception e) {
            log.error("记录权限检查日志失败 userId={}, permission={}", userId, permission, e);
            // 记录日志失败的情况下，尝试记录一条失败状态的日志
            try {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("permission", permission);
                errorDetails.put("granted", granted);
                errorDetails.put("error", e.getMessage());

                SystemPermissionLog errorLog = createLog(
                        SystemPermissionLog.OperationType.PERMISSION_CHECK,
                        granted ? SystemPermissionLog.OperationResult.SUCCESS : SystemPermissionLog.OperationResult.DENIED,
                        SystemPermissionLog.RecordStatus.RECORD_FAILED,
                        userId, userLogin, null, null, null, null, null, permission,
                        String.format("权限检查 %s - %s (记录失败)", permission, granted ? "授予" : "拒绝"),
                        errorDetails, null, null, null, operationDuration, e.getMessage(), null
                );

                permissionLogRepository.sql().saveCommand(errorLog)
                        .setMode(SaveMode.INSERT_ONLY)
                        .execute();
            } catch (Exception ex) {
                log.error("记录权限检查失败日志也失败了 userId={}, permission={}", userId, permission, ex);
            }
        }
    }

    @Override
    @Async
    public void logDataScopeApplied(@NotNull Long userId, @NotNull String userLogin,
                                   @NotNull String dataScopeType, @Nullable String sqlCondition,
                                   @Nullable Long operationDuration) {
        try {
            Map<String, Object> details = new HashMap<>();
            details.put("dataScopeType", dataScopeType);
            details.put("sqlCondition", sqlCondition);

            SystemPermissionLog auditLog = createLog(
                    SystemPermissionLog.OperationType.DATA_SCOPE_APPLIED,
                    SystemPermissionLog.OperationResult.SUCCESS,
                    SystemPermissionLog.RecordStatus.RECORDED,
                    userId, userLogin, null, null, null, null, null, null,
                    String.format("应用数据权限: %s", dataScopeType),
                    details, null, null, null, operationDuration, null, null
            );

            permissionLogRepository.sql().saveCommand(auditLog)
                    .setMode(SaveMode.INSERT_ONLY)
                    .execute();
            log.debug("记录数据权限应用日志: userId={}, dataScopeType={}", userId, dataScopeType);
        } catch (Exception e) {
            log.error("记录数据权限应用日志失败: userId={}, dataScopeType={}", userId, dataScopeType, e);
        }
    }

    @Override
    @Async
    public void logResourceCreate(@NotNull Long operatorUserId, @NotNull String operatorUserLogin,
                                 @NotNull Long resourceId, @NotNull String permission,
                                 boolean success, @Nullable String errorMessage) {
        try {
            SystemPermissionLog auditLog = createLog(
                    SystemPermissionLog.OperationType.RESOURCE_CREATE,
                    success ? SystemPermissionLog.OperationResult.SUCCESS : SystemPermissionLog.OperationResult.FAILED,
                    SystemPermissionLog.RecordStatus.RECORDED,
                    operatorUserId, operatorUserLogin, null, null, null, null, resourceId, permission,
                    String.format("创建资源: %s", permission),
                    null, null, null, null, null, errorMessage, null
            );

            permissionLogRepository.sql().saveCommand(auditLog)
                    .setMode(SaveMode.INSERT_ONLY)
                    .execute();
            log.debug("记录资源创建日志: operatorUserId={}, resourceId={}, success={}",
                     operatorUserId, resourceId, success);
        } catch (Exception e) {
            log.error("记录资源创建日志失败: operatorUserId={}, resourceId={}", operatorUserId, resourceId, e);
        }
    }

    @Override
    @Async
    public void logResourceUpdate(@NotNull Long operatorUserId, @NotNull String operatorUserLogin,
                                 @NotNull Long resourceId, @NotNull String permission,
                                 @Nullable String changeDetails,
                                 boolean success, @Nullable String errorMessage) {
        try {
            Map<String, Object> details = new HashMap<>();
            if (changeDetails != null) {
                details.put("changes", changeDetails);
            }

            SystemPermissionLog auditLog = createLog(
                    SystemPermissionLog.OperationType.RESOURCE_UPDATE,
                    success ? SystemPermissionLog.OperationResult.SUCCESS : SystemPermissionLog.OperationResult.FAILED,
                    SystemPermissionLog.RecordStatus.RECORDED,
                    operatorUserId, operatorUserLogin, null, null, null, null, resourceId, permission,
                    String.format("更新资源: %s", permission),
                    details, null, null, null, null, errorMessage, null
            );

            permissionLogRepository.sql().saveCommand(auditLog)
                    .setMode(SaveMode.INSERT_ONLY)
                    .execute();
            log.debug("记录资源更新日志: operatorUserId={}, resourceId={}, success={}",
                     operatorUserId, resourceId, success);
        } catch (Exception e) {
            log.error("记录资源更新日志失败: operatorUserId={}, resourceId={}", operatorUserId, resourceId, e);
        }
    }

    @Override
    @Async
    public void logResourceDelete(@NotNull Long operatorUserId, @NotNull String operatorUserLogin,
                                 @NotNull Long resourceId, @NotNull String permission,
                                 boolean success, @Nullable String errorMessage) {
        try {
            SystemPermissionLog auditLog = createLog(
                    SystemPermissionLog.OperationType.RESOURCE_DELETE,
                    success ? SystemPermissionLog.OperationResult.SUCCESS : SystemPermissionLog.OperationResult.FAILED,
                    SystemPermissionLog.RecordStatus.RECORDED,
                    operatorUserId, operatorUserLogin, null, null, null, null, resourceId, permission,
                    String.format("删除资源: %s", permission),
                    null, null, null, null, null, errorMessage, null
            );

            permissionLogRepository.sql().saveCommand(auditLog)
                    .setMode(SaveMode.INSERT_ONLY)
                    .execute();
            log.debug("记录资源删除日志: operatorUserId={}, resourceId={}, success={}",
                     operatorUserId, resourceId, success);
        } catch (Exception e) {
            log.error("记录资源删除日志失败: operatorUserId={}, resourceId={}", operatorUserId, resourceId, e);
        }
    }

    @Override
    public Page<SystemPermissionLog> queryLogs(@Nullable String operationType,
                                              @Nullable String operationResult,
                                              @Nullable Long operatorUserId,
                                              @Nullable Long targetUserId,
                                              @Nullable LocalDateTime startTime,
                                              @Nullable LocalDateTime endTime,
                                              @NotNull Pageable pageable) {
        return permissionLogRepository.paginate(
                operationType, operationResult, null, operatorUserId, targetUserId,
                startTime, endTime, pageable, null
        );
    }

    @Override
    public Page<SystemPermissionLog> queryLogs(@Nullable String operationType,
                                               @Nullable String operationResult,
                                               @Nullable String recordStatus,
                                               @Nullable Long operatorUserId,
                                               @Nullable Long targetUserId,
                                               @Nullable LocalDateTime startTime,
                                               @Nullable LocalDateTime endTime,
                                               @NotNull Pageable pageable) {
        return permissionLogRepository.paginate(
                operationType, operationResult, recordStatus, operatorUserId, targetUserId,
                startTime, endTime, pageable, null
        );
    }

    @Override
    public List<SystemPermissionLog> getUserOperationLogs(@NotNull Long userId, int limit) {
        List<SystemPermissionLog> logs = permissionLogRepository.findByOperatorUserId(userId, null);
        return logs.stream().limit(limit).collect(Collectors.toList());
    }

    @Override
    public List<SystemPermissionLog> getUserTargetLogs(@NotNull Long userId, int limit) {
        List<SystemPermissionLog> logs = permissionLogRepository.findByTargetUserId(userId, null);
        return logs.stream().limit(limit).collect(Collectors.toList());
    }

    @Override
    public List<SystemPermissionLog> getFailedOperations(@Nullable LocalDateTime since, int limit) {
        List<SystemPermissionLog> logs = permissionLogRepository.findFailedOperations(since, null);
        return logs.stream().limit(limit).collect(Collectors.toList());
    }

    @Override
    public List<SystemPermissionLog> getRecentPermissionDenied(@NotNull Long userId,
                                                              @NotNull LocalDateTime since) {
        return permissionLogRepository.findRecentPermissionDenied(userId, since, null);
    }


    @Override
    public Map<String, Long> getOperationTypeStatistics(@Nullable LocalDateTime startTime,
                                                        @Nullable LocalDateTime endTime) {
        List<Tuple2<SystemPermissionLog.OperationType, Long>> results = permissionLogRepository.countByOperationType(startTime, endTime);

        Map<String, Long> statistics = new HashMap<>();
        for (Tuple2<SystemPermissionLog.OperationType, Long> result : results) {
            SystemPermissionLog.OperationType operationType = result.component1();
            Long count = result.component2();
            statistics.put(operationType.displayName(), count);
        }

        return statistics;
    }

    @Override
    public List<Map<String, Object>> getUserOperationRanking(@Nullable LocalDateTime startTime,
                                                             @Nullable LocalDateTime endTime,
                                                             int limit) {
        List<Tuple3<Long, String, Long>> results = permissionLogRepository.countByOperatorUser(startTime, endTime, limit);

        return results.stream().map(result -> {
            Map<String, Object> item = new HashMap<>();
            item.put("userId", result.component1());
            item.put("userLogin", result.component2());
            item.put("operationCount", result.component3());
            return item;
        }).collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> getAuditOverview(@Nullable LocalDateTime startTime,
                                               @Nullable LocalDateTime endTime) {
        Map<String, Object> overview = new HashMap<>();

        // 操作类型统计
        Map<String, Long> operationTypeStats = getOperationTypeStatistics(startTime, endTime);
        overview.put("operationTypeStatistics", operationTypeStats);

        // 总操作次
        long totalOperations = operationTypeStats.values().stream().mapToLong(Long::longValue).sum();
        overview.put("totalOperations", totalOperations);

        // 失败操作次数
        List<SystemPermissionLog> failedOps = getFailedOperations(startTime, Integer.MAX_VALUE);
        overview.put("failedOperations", failedOps.size());

        // 成功率
        double successRate = totalOperations > 0 ? (double) (totalOperations - failedOps.size()) / totalOperations * 100 : 0;
        overview.put("successRate", Math.round(successRate * 100.0) / 100.0);

        // 用户操作排行
        List<Map<String, Object>> userRanking = getUserOperationRanking(startTime, endTime, 10);
        overview.put("topUsers", userRanking);

        return overview;
    }

    @Override
    public int cleanupExpiredLogs(@NotNull LocalDateTime beforeTime) {
        int deletedCount = permissionLogRepository.deleteByCreateTimeBefore(beforeTime);
        log.info("清理过期权限日志完成: 删除 {} 条记录", deletedCount);

        return deletedCount;
    }

    @Override
    public void exportLogs(@Nullable LocalDateTime startTime,
                          @Nullable LocalDateTime endTime,
                          @NotNull String exportPath) {
        // TODO: 实现日志导出功能
        log.info("导出权限日志: startTime={}, endTime={}, exportPath={}",
                startTime, endTime, exportPath);
    }

    private String toJsonString(Map<String, Object> data) {
        try {
            return objectMapper.writeValueAsString(data);
        } catch (JsonProcessingException e) {
            log.warn("转换操作详情为JSON失败", e);
            return data.toString();
        }
    }
}
