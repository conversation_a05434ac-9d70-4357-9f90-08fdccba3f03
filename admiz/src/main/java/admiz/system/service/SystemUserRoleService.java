package admiz.system.service;

import admiz.system.model.SystemRole;
import org.jetbrains.annotations.NotNull;

import java.util.List;

public interface SystemUserRoleService {

    List<SystemRole> getUserRoles(@NotNull Long userId);

    boolean assignRoles(@NotNull Long userId, @NotNull List<Long> roleIds);

    boolean removeRoles(@NotNull Long userId, @NotNull List<Long> roleIds);

    boolean batchAssignRoles(@NotNull List<Long> userIds, @NotNull List<Long> roleIds);

    boolean batchRemoveRoles(@NotNull List<Long> userIds, @NotNull List<Long> roleIds);

    boolean batchAddRoles(@NotNull List<Long> userIds, @NotNull List<Long> roleIds);
}
