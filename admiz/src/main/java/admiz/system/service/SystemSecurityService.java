package admiz.system.service;

import admiz.auth.AuthContext;
import admiz.auth.shiro.CustomShiroSubject;
import admiz.common.auth.SecurityMixin;
import admiz.common.errorhandling.*;
import admiz.system.model.Immutables;
import admiz.system.model.SystemUser;
import admiz.system.model.SystemUserAuthn;
import admiz.system.repository.SystemUserAuthnRepository;
import admiz.system.repository.SystemUserRepository;
import admiz.utils.ServletUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.subject.Subject;
import org.babyfish.jimmer.sql.ast.mutation.SaveMode;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.Optional;


@Slf4j
@RequiredArgsConstructor @Service
public class SystemSecurityService implements SecurityMixin {
    final SystemUserRepository userRepository;
    final SystemUserAuthnRepository authnRepository;
    final SystemCaptchaService captchaService;
    final SystemPermissionAuditService auditService;

    public SystemUser loginByPassword(@NotNull String login, @NotNull String password, @NotNull String captcha, boolean rememberMe) {
        boolean isCaptchaValid = captchaService.validateCaptcha(AuthContext.sessionId(), captcha);
        if(!isCaptchaValid) {
            throw AppErrors.Auth.INCORRECT_CAPTCHA.toException(ErrorTag.of("captcha", null));
        }

        UsernamePasswordToken token = new UsernamePasswordToken(login, password, rememberMe);

        try {
            Subject subject1 = SecurityUtils.getSubject();
            CustomShiroSubject subject = (CustomShiroSubject) subject1;
            subject.login(token);
            SystemUser currentUser = subject.getCurrentUser();
            boolean permitted = subject.isPermitted("ROLE_ADMIN");

            // 记录登录成功日志
            try {
                String clientIp = ServletUtils.getClientIp();
                String userAgent = ServletUtils.getUserAgent();
                String sessionId = subject.getSession().getId().toString();
                auditService.logUserLogin(currentUser.id(), currentUser.login(),
                        clientIp, userAgent, sessionId, true, null);
            } catch (Exception auditEx) {
                log.warn("记录用户登录成功审计日志失败", auditEx);
            }

            return currentUser;
        } catch (ErrorDetailException e) {
            throw e;
        } catch (AuthErrorDetailException e) {
            // 只有账号锁定才返回具体错误信息
            if(e.getErrorDetail() == AppErrors.Auth.ACCOUNT_LOCKED) {
                throw e;
            } else {
                // 其余情况全部返回模糊的“账号或密码错误”，防止用户名猜测等
                throw AppErrors.Auth.INCORRECT_CREDENTIALS.toException();
            }
        } catch (AuthenticationException e) {
            log.debug("Login failed: login={}, captcha={}", login, captcha);

            // 记录登录失败日志
            try {
                // 尝试获取用户信息用于日志记录
                SystemUserAuthn userAuthn = authnRepository.findByAuthnIdAndAuthnType(login, SystemUserAuthn.AuthnType.PASSWORD).orElse(null);
                if (userAuthn != null) {
                    SystemUser user = userRepository.findById(userAuthn.userId()).orElse(null);
                    if (user != null) {
                        String clientIp = ServletUtils.getClientIp();
                        String userAgent = ServletUtils.getUserAgent();
                        auditService.logUserLogin(user.id(), user.login(),
                                clientIp, userAgent, null, false, "认证失败");
                    }
                }
            } catch (Exception auditEx) {
                log.warn("记录用户登录失败审计日志失败", auditEx);
            }

            throw AppErrors.Auth.INCORRECT_CREDENTIALS.toException();
        } catch (Exception ex) {
            log.error("登录发生错误: login={}, captcha={}", login, captcha, ex);
            throw AppErrors.CommonError.SERVER_ERROR.toException();
        }
    }

    public boolean changePassword(@NotNull long userId, @NotNull String newPassword) {
        try {
            SystemUser user = userRepository.findById(userId).orElseThrow(AppErrors.CommonError.NOT_FOUND::toException);

            String encryptedPassword = getPasswordService().encryptPassword(newPassword);

            Optional<SystemUserAuthn> userAuthn = authnRepository.findByUserIdAndAuthnType(userId, SystemUserAuthn.AuthnType.PASSWORD);

            if(userAuthn.isPresent()) {
                SystemUserAuthn update = Immutables.createSystemUserAuthn(draft -> {
                    draft.setAuthnId(user.login())
                            .setAuthnType(SystemUserAuthn.AuthnType.PASSWORD)
                            .setAuthnData(encryptedPassword);
                });
                SystemUserAuthn saved = authnRepository.save(update, SaveMode.UPDATE_ONLY);
            } else {
                SystemUserAuthn newUserAuthn = Immutables.createSystemUserAuthn(draft -> {
                    draft.setUserId(userId)
                            .setAuthnId(user.login())
                            .setAuthnType(SystemUserAuthn.AuthnType.PASSWORD)
                            .setAuthnData(encryptedPassword)
                            .setDisabled(false);
                });
                SystemUserAuthn saved = authnRepository.save(newUserAuthn, SaveMode.UPSERT);
            }


            return true;
        } catch (Exception ex) {
            log.error("修改密码发生错误: opUser={}, targetUserId={}", AuthContext.currentUser().toStringInfo(), userId, ex);
            throw AppErrors.CommonError.SERVER_ERROR.toException();
        }
    }
}
