package admiz.system.service;

import admiz.system.model.SystemResource;
import admiz.system.model.SystemRole;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;

// 系统权限验证服务接口
public interface SystemPermissionService {

    // 检查用户是否拥有指定权限
    boolean hasPermission(@NotNull Long userId, @NotNull String permission);

    // 检查用户是否拥有任意一个权限
    boolean hasAnyPermission(@NotNull Long userId, @NotNull List<String> permissions);

    // 检查用户是否拥有所有权限
    boolean hasAllPermissions(@NotNull Long userId, @NotNull List<String> permissions);

    // 检查用户是否拥有指定角色
    boolean hasRole(@NotNull Long userId, @NotNull String roleName);

    // 检查用户是否拥有任意一个角色
    boolean hasAnyRole(@NotNull Long userId, @NotNull List<String> roleNames);

    // 检查用户是否拥有所有角色
    boolean hasAllRoles(@NotNull Long userId, @NotNull List<String> roleNames);

    // 获取当前用户的所有权限
    List<SystemResource> getCurrentUserPermissions();

    // 获取当前用户的所有角色
    List<SystemRole> getCurrentUserRoles();

    /**
     * 获取用户的所有权限
     * 
     * @param userId 用户ID
     * @return 权限列表
     */
    List<SystemResource> getUserPermissions(@NotNull Long userId);

    /**
     * 获取用户的所有角色
     * 
     * @param userId 用户ID
     * @return 角色列表
     */
    List<SystemRole> getUserRoles(@NotNull Long userId);

    /**
     * 获取用户的菜单权限
     * 
     * @param userId 用户ID
     * @return 菜单权限列表
     */
    List<SystemResource> getUserMenuPermissions(@NotNull Long userId);

    /**
     * 获取当前用户的菜单权限
     * 
     * @return 菜单权限列表
     */
    List<SystemResource> getCurrentUserMenuPermissions();

    /**
     * 检查当前用户是否拥有指定权限
     * 
     * @param permission 权限编码
     * @return 是否拥有权限
     */
    boolean currentUserHasPermission(@NotNull String permission);

    /**
     * 检查当前用户是否拥有任意一个权限
     * 
     * @param permissions 权限编码列表
     * @return 是否拥有任意一个权限
     */
    boolean currentUserHasAnyPermission(@NotNull List<String> permissions);

    /**
     * 检查当前用户是否拥有所有权限
     * 
     * @param permissions 权限编码列表
     * @return 是否拥有所有权限
     */
    boolean currentUserHasAllPermissions(@NotNull List<String> permissions);

    /**
     * 检查当前用户是否拥有指定角色
     * 
     * @param roleName 角色名称
     * @return 是否拥有角色
     */
    boolean currentUserHasRole(@NotNull String roleName);

    /**
     * 检查当前用户是否拥有任意一个角色
     * 
     * @param roleNames 角色名称列表
     * @return 是否拥有任意一个角色
     */
    boolean currentUserHasAnyRole(@NotNull List<String> roleNames);

    /**
     * 检查当前用户是否拥有所有角色
     * 
     * @param roleNames 角色名称列表
     * @return 是否拥有所有角色
     */
    boolean currentUserHasAllRoles(@NotNull List<String> roleNames);

    /**
     * 获取当前登录用户ID
     *
     * @return 用户ID，如果未登录返回null
     */
    @Nullable
    Long getCurrentUserId();

    /**
     * 获取当前登录用户的登录名
     *
     * @return 用户登录名，如果未登录返回null
     */
    @Nullable
    String getCurrentUserLogin();

    /**
     * 检查是否已登录
     *
     * @return 是否已登录
     */
    boolean isAuthenticated();

    /**
     * 检查是否是超级管理员
     * 
     * @param userId 用户ID
     * @return 是否是超级管理员
     */
    boolean isSuperAdmin(@NotNull Long userId);

    /**
     * 检查当前用户是否是超级管理员
     * 
     * @return 是否是超级管理员
     */
    boolean currentUserIsSuperAdmin();
}
