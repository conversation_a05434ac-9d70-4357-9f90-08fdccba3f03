package admiz.system.service;

import admiz.system.repository.SystemKvStoreRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

@Slf4j @RequiredArgsConstructor
public class SystemKvStoreService {
    final SystemKvStoreRepository kvStoreRepository;

    public int evictExpiredKvItems() {
        return kvStoreRepository.removeAllByExpireTime(LocalDateTime.now());
    }
}
