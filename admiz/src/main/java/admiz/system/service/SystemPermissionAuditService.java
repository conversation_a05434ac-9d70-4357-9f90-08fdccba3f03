package admiz.system.service;

import admiz.system.model.SystemPermissionLog;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

// 系统权限审计服务接口
public interface SystemPermissionAuditService {

    // 记录用户登录日志
    void logUserLogin(@NotNull Long userId, @NotNull String userLogin, 
                     @Nullable String clientIp, @Nullable String userAgent, 
                     @Nullable String sessionId, boolean success, @Nullable String errorMessage);

    // 记录用户登出日志
    void logUserLogout(@NotNull Long userId, @NotNull String userLogin, 
                      @Nullable String sessionId);

    // 记录用户角色分配日志
    void logUserRoleAssign(@NotNull Long operatorUserId, @NotNull String operatorUserLogin,
                          @NotNull Long targetUserId, @NotNull String targetUserLogin,
                          @NotNull List<Long> roleIds, @NotNull List<String> roleNames,
                          boolean success, @Nullable String errorMessage);

    // 记录用户角色移除日志
    void logUserRoleRemove(@NotNull Long operatorUserId, @NotNull String operatorUserLogin,
                          @NotNull Long targetUserId, @NotNull String targetUserLogin,
                          @NotNull List<Long> roleIds, @NotNull List<String> roleNames,
                          boolean success, @Nullable String errorMessage);

    // 记录用户管理操作日志（禁用、启用、更新等）
    void logUserManagement(@NotNull Long operatorUserId, @NotNull String operatorUserLogin,
                           @NotNull Long targetUserId, @NotNull String targetUserLogin,
                           @NotNull String operationType, @NotNull String operationDescription,
                           boolean success, @Nullable String errorMessage);

    // 记录角色创建日志
    void logRoleCreate(@NotNull Long operatorUserId, @NotNull String operatorUserLogin,
                      @NotNull Long roleId, @NotNull String roleName,
                      boolean success, @Nullable String errorMessage);

    // 记录角色更新日志
    void logRoleUpdate(@NotNull Long operatorUserId, @NotNull String operatorUserLogin,
                      @NotNull Long roleId, @NotNull String roleName,
                      @Nullable String changeDetails,
                      boolean success, @Nullable String errorMessage);

    // 记录角色删除日志
    void logRoleDelete(@NotNull Long operatorUserId, @NotNull String operatorUserLogin,
                      @NotNull Long roleId, @NotNull String roleName,
                      boolean success, @Nullable String errorMessage);

    // 记录角色权限分配日志
    void logRolePermissionAssign(@NotNull Long operatorUserId, @NotNull String operatorUserLogin,
                                @NotNull Long roleId, @NotNull String roleName,
                                @NotNull List<Long> resourceIds, @NotNull List<String> permissions,
                                boolean success, @Nullable String errorMessage);

    // 记录权限验证日志
    void logPermissionCheck(@NotNull Long userId, @NotNull String userLogin,
                           @NotNull String permission, boolean granted,
                           @Nullable Long operationDuration);

    // 记录数据权限应用日志
    void logDataScopeApplied(@NotNull Long userId, @NotNull String userLogin,
                            @NotNull String dataScopeType, @Nullable String sqlCondition,
                            @Nullable Long operationDuration);

    // 记录资源创建日志
    void logResourceCreate(@NotNull Long operatorUserId, @NotNull String operatorUserLogin,
                          @NotNull Long resourceId, @NotNull String permission,
                          boolean success, @Nullable String errorMessage);

    // 记录资源更新日志
    void logResourceUpdate(@NotNull Long operatorUserId, @NotNull String operatorUserLogin,
                          @NotNull Long resourceId, @NotNull String permission,
                          @Nullable String changeDetails,
                          boolean success, @Nullable String errorMessage);

    // 记录资源删除日志
    void logResourceDelete(@NotNull Long operatorUserId, @NotNull String operatorUserLogin,
                          @NotNull Long resourceId, @NotNull String permission,
                          boolean success, @Nullable String errorMessage);


    //    // 分页查询权限日志
    Page<SystemPermissionLog> queryLogs(@Nullable String operationType,
                                       @Nullable String operationResult,
                                       @Nullable Long operatorUserId,
                                       @Nullable Long targetUserId,
                                       @Nullable LocalDateTime startTime,
                                       @Nullable LocalDateTime endTime,
                                       @NotNull Pageable pageable);

    // 分页查询权限日志（支持记录状态过滤）
    Page<SystemPermissionLog> queryLogs(@Nullable String operationType,
                                        @Nullable String operationResult,
                                        @Nullable String recordStatus,
                                        @Nullable Long operatorUserId,
                                        @Nullable Long targetUserId,
                                        @Nullable LocalDateTime startTime,
                                        @Nullable LocalDateTime endTime,
                                        @NotNull Pageable pageable);

    // 查询用户的操作日志
    List<SystemPermissionLog> getUserOperationLogs(@NotNull Long userId, int limit);

    // 查询针对用户的操作日志
    List<SystemPermissionLog> getUserTargetLogs(@NotNull Long userId, int limit);

    // 查询失败的权限操作
    List<SystemPermissionLog> getFailedOperations(@Nullable LocalDateTime since, int limit);

    // 查询最近的权限拒绝记录
    List<SystemPermissionLog> getRecentPermissionDenied(@NotNull Long userId, 
                                                       @NotNull LocalDateTime since);

    // 统计操作类型分布
    Map<String, Long> getOperationTypeStatistics(@Nullable LocalDateTime startTime,
                                                 @Nullable LocalDateTime endTime);

    // 统计用户操作次数排行
    List<Map<String, Object>> getUserOperationRanking(@Nullable LocalDateTime startTime,
                                                      @Nullable LocalDateTime endTime,
                                                      int limit);

    // 获取权限审计概览
    Map<String, Object> getAuditOverview(@Nullable LocalDateTime startTime,
                                        @Nullable LocalDateTime endTime);


    // 清理过期日志
    int cleanupExpiredLogs(@NotNull LocalDateTime beforeTime);

    // 导出权限日志
    void exportLogs(@Nullable LocalDateTime startTime,
                   @Nullable LocalDateTime endTime,
                   @NotNull String exportPath);
}
