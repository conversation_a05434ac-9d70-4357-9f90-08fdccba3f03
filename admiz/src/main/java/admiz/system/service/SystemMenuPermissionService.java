package admiz.system.service;

import admiz.system.model.SystemResource;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;

// 菜单权限服务接口
public interface SystemMenuPermissionService {

    // 获取用户的菜单权限树
    List<SystemResource> getUserMenuTree(@NotNull Long userId);

    // 获取当前用户的菜单权限树
    List<SystemResource> getCurrentUserMenuTree();

    // 构建菜单树结构
    List<SystemResource> buildMenuTree(@NotNull List<SystemResource> menuResources);

    // 过滤用户可见的菜单
    List<SystemResource> filterVisibleMenus(@NotNull Long userId, @NotNull List<SystemResource> allMenus);

    // 检查菜单是否对用户可见
    boolean isMenuVisible(@NotNull Long userId, @NotNull Long menuId);

    // 获取用户的顶级菜单
    List<SystemResource> getUserTopLevelMenus(@NotNull Long userId);

    // 获取菜单的子菜单
    List<SystemResource> getSubMenus(@NotNull Long parentMenuId, @Nullable Long userId);
}
