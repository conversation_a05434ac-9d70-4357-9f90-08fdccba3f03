package admiz.system.service;

import admiz.auth.AuthContext;
import admiz.common.auth.SecurityMixin;
import admiz.common.auth.UserDetailsProvider;
import admiz.common.errorhandling.*;
import admiz.system.model.Immutables;
import admiz.system.model.SystemUser;
import admiz.system.model.SystemUserAuthn;
import admiz.system.repository.SystemUserAuthnRepository;
import admiz.system.repository.SystemUserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.subject.Subject;
import org.babyfish.jimmer.sql.ast.mutation.SaveMode;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.Optional;


@Slf4j
@RequiredArgsConstructor @Service
public class SystemUserService implements UserDetailsProvider, SecurityMixin {
    final SystemUserRepository userRepository;
    final SystemUserAuthnRepository authnRepository;
    final SystemCaptchaService captchaService;

    public SystemUser loginByPassword(@NotNull String login, @NotNull String password, @NotNull String captcha, boolean rememberMe) {

        boolean isCaptchaValid = captchaService.validateCaptcha(AuthContext.sessionId(), captcha);
        if(!isCaptchaValid) {
            throw AppErrors.Auth.INCORRECT_CAPTCHA.toException(ErrorTag.of("captcha", null));
        }

        UsernamePasswordToken token = new UsernamePasswordToken(login, password, rememberMe);

        try {
            Subject subject = SecurityUtils.getSubject();
            subject.login(token);
            long userId = (Long) subject.getPrincipal();
            boolean permitted = subject.isPermitted("ROLE_ADMIN");
            return userRepository.findById(userId).orElseThrow(AppErrors.Auth.INCORRECT_CREDENTIALS::toException);
        } catch (ErrorDetailException e) {
            throw e;
        } catch (AuthErrorDetailException e) {
            // 只有账号锁定才返回具体错误信息
            if(e.getErrorDetail() == AppErrors.Auth.ACCOUNT_LOCKED) {
                throw e;
            } else {
                // 其余情况全部返回模糊的“账号或密码错误”，防止用户名猜测等
                throw AppErrors.Auth.INCORRECT_CREDENTIALS.toException();
            }
        } catch (AuthenticationException e) {
            log.debug("Login failed: login={}, captcha={}", login, captcha);
            throw AppErrors.Auth.INCORRECT_CREDENTIALS.toException();
        } catch (Exception ex) {
            log.error("登录发生错误: login={}, captcha={}", login, captcha, ex);
            throw AppErrors.CommonError.SERVER_ERROR.toException();
        }
    }

    public boolean changePassword(@NotNull long userId, @NotNull String newPassword) {
        try {
            SystemUser user = userRepository.findById(userId).orElseThrow(AppErrors.CommonError.NOT_FOUND::toException);

            String encryptedPassword = getPasswordService().encryptPassword(newPassword);

            Optional<SystemUserAuthn> userAuthn = authnRepository.findByUserIdAndAuthnType(userId, SystemUserAuthn.AuthnType.PASSWORD);

            if(userAuthn.isPresent()) {
                SystemUserAuthn update = Immutables.createSystemUserAuthn(draft -> {
                    draft.setAuthnId(user.login())
                            .setAuthnType(SystemUserAuthn.AuthnType.PASSWORD)
                            .setAuthnData(encryptedPassword);
                });
                SystemUserAuthn saved = authnRepository.save(update, SaveMode.UPDATE_ONLY);
            } else {
                SystemUserAuthn newUserAuthn = Immutables.createSystemUserAuthn(draft -> {
                    draft.setUserId(userId)
                            .setAuthnId(user.login())
                            .setAuthnType(SystemUserAuthn.AuthnType.PASSWORD)
                            .setAuthnData(encryptedPassword)
                            .setDisabled(false);
                });
                SystemUserAuthn saved = authnRepository.save(newUserAuthn, SaveMode.UPSERT);
            }


            return true;
        } catch (Exception ex) {
            log.error("修改密码发生错误: opUser={}, targetUserId={}", AuthContext.currentUser().toStringInfo(), userId, ex);
            throw AppErrors.CommonError.SERVER_ERROR.toException();
        }
    }

    @Override
    public SystemUser getUser(long userId, String realmName) {
        SystemUser user = userRepository.findById(userId).orElseThrow(AppErrors.CommonError.NOT_FOUND::toException);
        return user;
    }
}
