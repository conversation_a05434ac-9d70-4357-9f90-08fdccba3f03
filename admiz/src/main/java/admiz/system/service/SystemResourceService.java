package admiz.system.service;

import admiz.system.model.SystemResource;
import admiz.system.model.dto.CreateSystemResourceInput;
import admiz.system.model.dto.UpdateSystemResourceInput;
import org.babyfish.jimmer.sql.fetcher.Fetcher;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

// 系统资源管理服务接口
public interface SystemResourceService {

    // 创建资源
    SystemResource createResource(@NotNull CreateSystemResourceInput input);

    // 更新资源
    SystemResource updateResource(@NotNull UpdateSystemResourceInput input);

    // 删除资源
    boolean deleteResource(@NotNull Long resourceId);

    // 根据ID查询资源
    Optional<SystemResource> findById(@NotNull Long resourceId, @Nullable Fetcher<SystemResource> fetcher);

    // 根据ID查询资源（必须存在）
    SystemResource getById(@NotNull Long resourceId, @Nullable Fetcher<SystemResource> fetcher);

    // 根据权限编码查询资源
    List<SystemResource> findByPermission(@NotNull String permission);

    // 分页查询资源列表
    Page<SystemResource> paginate(@Nullable String searchText,
                                 @Nullable SystemResource.ResourceType resourceType,
                                 @Nullable Boolean disabled,
                                 @NotNull Pageable pageable,
                                 @Nullable Fetcher<SystemResource> fetcher);

    // 查询所有可用资源
    List<SystemResource> findAllAvailable(@Nullable Fetcher<SystemResource> fetcher);

    // 构建资源树结构
    List<SystemResource> buildResourceTree(@Nullable Fetcher<SystemResource> fetcher);

    // 根据父级ID查询子资源
    List<SystemResource> findByParentId(@Nullable Long parentId, @Nullable Fetcher<SystemResource> fetcher);

    // 查询菜单类型的资源
    List<SystemResource> findMenuResources(@Nullable Fetcher<SystemResource> fetcher);

    // 根据资源类型查询资源
    List<SystemResource> findByResourceType(@NotNull SystemResource.ResourceType resourceType,
                                           @Nullable Fetcher<SystemResource> fetcher);

    // 检查权限编码是否存在
    boolean existsByPermission(@NotNull String permission);

    // 检查权限编码是否存在（排除指定ID）
    boolean existsByPermissionAndIdNot(@NotNull String permission, @Nullable Long excludeId);

    // 根据用户ID查询用户拥有的资源权限
    List<SystemResource> findUserResources(@NotNull Long userId, @Nullable Fetcher<SystemResource> fetcher);

    // 根据用户ID查询菜单类型的资源
    List<SystemResource> findUserMenuResources(@NotNull Long userId, @Nullable Fetcher<SystemResource> fetcher);

    // 检查用户是否拥有指定权限
    boolean hasPermission(@NotNull Long userId, @NotNull String permission);

    // 检查用户是否拥有任意一个权限
    boolean hasAnyPermission(@NotNull Long userId, @NotNull List<String> permissions);

    // 检查用户是否拥有所有权限
    boolean hasAllPermissions(@NotNull Long userId, @NotNull List<String> permissions);


}
