package admiz.system.service;

import admiz.system.model.SystemResource;
import admiz.system.model.SystemRole;
import admiz.system.model.dto.BatchOperationResult;
import admiz.system.model.dto.CreateSystemRoleInput;
import admiz.system.model.dto.UpdateSystemRoleInput;
import org.babyfish.jimmer.sql.fetcher.Fetcher;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * 系统角色管理服务接口
 * 
 * <AUTHOR>
 */
public interface SystemRoleService {

    /**
     * 创建角色
     */
    SystemRole createRole(@NotNull CreateSystemRoleInput input);

    /**
     * 更新角色
     */
    SystemRole updateRole(@NotNull UpdateSystemRoleInput input);

    /**
     * 删除角色
     */
    boolean deleteRole(@NotNull Long roleId);

    /**
     * 根据ID查询角色
     */
    Optional<SystemRole> findById(@NotNull Long roleId, @Nullable Fetcher<SystemRole> fetcher);

    /**
     * 根据ID查询角色（必须存在）
     */
    SystemRole getById(@NotNull Long roleId, @Nullable Fetcher<SystemRole> fetcher);

    /**
     * 根据名称查询角色
     */
    Optional<SystemRole> findByName(@NotNull String name);

    /**
     * 检查角色名称是否存在
     */
    boolean existsByName(@NotNull String name);

    /**
     * 检查角色名称是否存在（排除指定ID）
     */
    boolean existsByNameAndIdNot(@NotNull String name, @Nullable Long excludeId);

    /**
     * 分页查询角色列表
     */
    Page<SystemRole> paginate(@Nullable String searchText,
                             @Nullable Boolean disabled,
                             @NotNull Pageable pageable,
                             @Nullable Fetcher<SystemRole> fetcher);

    /**
     * 查询所有可用角色
     */
    List<SystemRole> findAllAvailable(@Nullable Fetcher<SystemRole> fetcher);

    /**
     * 为角色分配权限
     */
    boolean assignPermissions(@NotNull Long roleId, @NotNull List<Long> resourceIds);

    /**
     * 获取角色的权限列表
     */
    List<SystemResource> getRolePermissions(@NotNull Long roleId);

    /**
     * 批量更新角色状态
     * @param roleIds 角色ID列表
     * @param disabled 是否禁用
     * @return 批量操作结果
     */
    BatchOperationResult batchUpdateRoleStatus(@NotNull List<Long> roleIds, @NotNull Boolean disabled);
}
