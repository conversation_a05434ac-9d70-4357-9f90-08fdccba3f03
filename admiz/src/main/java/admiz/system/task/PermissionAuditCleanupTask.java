package admiz.system.task;

import admiz.system.service.SystemPermissionAuditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 权限审计日志清理定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "admiz.audit.cleanup.enabled", havingValue = "true", matchIfMissing = true)
public class PermissionAuditCleanupTask {

    private final SystemPermissionAuditService auditService;

    /**
     * 清理过期的权限审计日志
     * 每天凌晨2点执行，清理90天前的日志
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupExpiredLogs() {
        try {
            log.info("开始清理过期权限审计日志");
            
            // 清理90天前的日志
            LocalDateTime beforeTime = LocalDateTime.now().minusDays(90);
            int deletedCount = auditService.cleanupExpiredLogs(beforeTime);
            
            log.info("权限审计日志清理完成，删除了 {} 条记录", deletedCount);
        } catch (Exception e) {
            log.error("清理过期权限审计日志失败", e);
        }
    }

    /**
     * 权限审计统计报告
     * 每周一上午9点执行
     */
    @Scheduled(cron = "0 0 9 * * MON")
    public void generateWeeklyReport() {
        try {
            log.info("开始生成权限审计周报");
            
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusDays(7);
            
            var overview = auditService.getAuditOverview(startTime, endTime);
            
            log.info("权限审计周报 - 时间范围: {} 到 {}", startTime, endTime);
            log.info("总操作次数: {}", overview.get("totalOperations"));
            log.info("失败操作次数: {}", overview.get("failedOperations"));
            log.info("成功率: {}%", overview.get("successRate"));
            
            // TODO: 可以发送邮件或推送到监控系统
            
        } catch (Exception e) {
            log.error("生成权限审计周报失败", e);
        }
    }

    /**
     * 检查异常权限操作
     * 每小时执行一次
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void checkAbnormalOperations() {
        try {
            LocalDateTime since = LocalDateTime.now().minusHours(1);
            
            // 检查失败操作
            var failedOps = auditService.getFailedOperations(since, 10);
            if (!failedOps.isEmpty()) {
                log.warn("发现 {} 个失败的权限操作", failedOps.size());
                // TODO: 可以发送告警
            }
            
        } catch (Exception e) {
            log.error("检查异常权限操作失败", e);
        }
    }
}
