package admiz.system.controller;

import admiz.common.result.Result;
import admiz.system.model.SystemResource;
import admiz.system.service.SystemMenuPermissionService;
import admiz.system.service.SystemPermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

// 菜单权限控制器
@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/api/menu")
public class MenuPermissionController {
    
    private final SystemMenuPermissionService menuPermissionService;
    private final SystemPermissionService permissionService;

    // 获取当前用户的菜单树
    @GetMapping("/tree")
    public Result<List<SystemResource>> getCurrentUserMenuTree() {
        try {
            if (!permissionService.isAuthenticated()) {
                return Result.fail(401, "用户未登录");
            }

            List<SystemResource> menuTree = menuPermissionService.getCurrentUserMenuTree();
            return Result.data(menuTree);
        } catch (Exception e) {
            log.error("获取当前用户菜单树失败", e);
            return Result.fail(500, "获取菜单失败: " + e.getMessage());
        }
    }

    // 获取指定用户的菜单树
    @GetMapping("/tree/{userId}")
    public Result<List<SystemResource>> getUserMenuTree(@PathVariable Long userId) {
        try {
            // 检查权限：只有管理员或用户本人可以查看
            Long currentUserId = permissionService.getCurrentUserId();
            if (currentUserId == null) {
                return Result.fail(401, "用户未登录");
            }

            if (!currentUserId.equals(userId) && !permissionService.currentUserIsSuperAdmin()) {
                return Result.fail(403, "权限不足");
            }

            List<SystemResource> menuTree = menuPermissionService.getUserMenuTree(userId);
            return Result.data(menuTree);
        } catch (Exception e) {
            log.error("获取用户菜单树失败: userId={}", userId, e);
            return Result.fail(500, "获取菜单失败: " + e.getMessage());
        }
    }

    // 获取当前用户的顶级菜单
    @GetMapping("/top-level")
    public Result<List<SystemResource>> getCurrentUserTopLevelMenus() {
        try {
            Long currentUserId = permissionService.getCurrentUserId();
            if (currentUserId == null) {
                return Result.fail(401, "用户未登录");
            }

            List<SystemResource> topMenus = menuPermissionService.getUserTopLevelMenus(currentUserId);
            return Result.data(topMenus);
        } catch (Exception e) {
            log.error("获取当前用户顶级菜单失败", e);
            return Result.fail(500, "获取顶级菜单失败: " + e.getMessage());
        }
    }

    // 获取子菜单
    @GetMapping("/sub-menus/{parentMenuId}")
    public Result<List<SystemResource>> getSubMenus(@PathVariable Long parentMenuId) {
        try {
            Long currentUserId = permissionService.getCurrentUserId();
            if (currentUserId == null) {
                return Result.fail(401, "用户未登录");
            }

            List<SystemResource> subMenus = menuPermissionService.getSubMenus(parentMenuId, currentUserId);
            return Result.data(subMenus);
        } catch (Exception e) {
            log.error("获取子菜单失败: parentMenuId={}", parentMenuId, e);
            return Result.fail(500, "获取子菜单失败: " + e.getMessage());
        }
    }

    // 检查菜单是否可见
    @GetMapping("/visible/{menuId}")
    public Result<Boolean> isMenuVisible(@PathVariable Long menuId) {
        try {
            Long currentUserId = permissionService.getCurrentUserId();
            if (currentUserId == null) {
                return Result.fail(401, "用户未登录");
            }

            boolean visible = menuPermissionService.isMenuVisible(currentUserId, menuId);
            return Result.data(visible);
        } catch (Exception e) {
            log.error("检查菜单可见性失败: menuId={}", menuId, e);
            return Result.fail(500, "检查菜单可见性失败: " + e.getMessage());
        }
    }

    // 获取用户权限信息
    @GetMapping("/permissions")
    public Result<List<String>> getCurrentUserPermissions() {
        try {
            Long currentUserId = permissionService.getCurrentUserId();
            if (currentUserId == null) {
                return Result.fail(401, "用户未登录");
            }

            List<String> permissions = permissionService.getUserPermissions(currentUserId)
                    .stream()
                    .map(SystemResource::permission)
                    .toList();

            return Result.data(permissions);
        } catch (Exception e) {
            log.error("获取用户权限信息失败", e);
            return Result.fail(500, "获取权限信息失败: " + e.getMessage());
        }
    }

    // 获取用户角色信息
    @GetMapping("/roles")
    public Result<List<String>> getCurrentUserRoles() {
        try {
            Long currentUserId = permissionService.getCurrentUserId();
            if (currentUserId == null) {
                return Result.fail(401, "用户未登录");
            }

            List<String> roles = permissionService.getUserRoles(currentUserId)
                    .stream()
                    .map(role -> role.name())
                    .toList();

            return Result.data(roles);
        } catch (Exception e) {
            log.error("获取用户角色信息失败", e);
            return Result.fail(500, "获取角色信息失败: " + e.getMessage());
        }
    }
}
