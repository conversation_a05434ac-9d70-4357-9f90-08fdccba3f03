package admiz.system.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@Controller
@Slf4j @RequiredArgsConstructor
public class ErrorController {
    @GetMapping("/error/{errorCode}")
    public String errorPage(@PathVariable String errorCode, Model model) {
        return "/error/error." + errorCode + ".peb.html";
    }
}
