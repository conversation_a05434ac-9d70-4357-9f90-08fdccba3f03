package admiz.system.controller;

import admiz.auth.AuthContext;
import admiz.module.ModuleMenu;
import admiz.module.SubSystem;
import admiz.system.repository.SystemResourceRepository;
import admiz.system.repository.SystemRoleRepository;
import admiz.system.service.SystemCaptchaService;
import admiz.system.service.SystemMenuService;
import admiz.system.service.SystemSecurityService;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;
import java.util.Map;

@Controller
@Slf4j @RequiredArgsConstructor
public class HomeController {
    final SystemCaptchaService captchaService;
    final SystemSecurityService securityService;
    final SystemRoleRepository roleRepository;
    final SystemResourceRepository resourceRepository;
    final SystemMenuService menuService;

    @GetMapping("/home")
    public String login(Model model, HttpServletRequest request) {
        model.addAttribute("subSystems", menuService.getSubSystems());

        Map<SubSystem, List<ModuleMenu>> subSystems = menuService.getAllSystemMenus();
        model.addAttribute("allSystemMenus", subSystems);

        Object activeSubSystemAttr = AuthContext.session().getAttribute("activeSubSystem");
        SubSystem activeSubSystem = SubSystem.fromString((String) activeSubSystemAttr);
        model.addAttribute("activeSubSystem", activeSubSystem);

        Cookie[] cookies = request.getCookies();
        return "/home/<USER>";
    }
    @GetMapping("/home/<USER>")
    public String dashboard() {
        return "/home/<USER>";
    }
}
