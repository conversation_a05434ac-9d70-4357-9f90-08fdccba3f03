package admiz.system.controller.mgmt;

import admiz.common.auth.RequiresPermission;
import admiz.common.result.Result;
import admiz.system.model.SystemResource;
import admiz.system.model.dto.CreateSystemResourceInput;
import admiz.system.model.dto.UpdateSystemResourceInput;
import admiz.system.service.SystemResourceService;
import com.google.common.base.Strings;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.*;
import java.util.stream.Collectors;

@Controller
@Slf4j
@RequiredArgsConstructor
@RequiresPermission("system:resource:view")
public class ResourceManagementController {

    private final SystemResourceService resourceService;

    public record ResourceIndexVo(String searchText, String resourceType, String disabled, String sort) {}

    @GetMapping("/system/mgmt/resources")
    public String index(Model model, ResourceIndexVo resourceIndexVo) {
        // 获取树状结构的资源数据
        List<SystemResource> resourceTree = resourceService.buildResourceTree(null);

        model.addAttribute("resourceTree", resourceTree);
        model.addAttribute("resourceIndexVo", resourceIndexVo);

        return "/system/mgmt/resources/index/index.peb.html";
    }

    private Page<SystemResource> findResources(String searchText, String resourceType, String disabled, Pageable pageable) {
        SystemResource.ResourceType resourceTypeEnum = null;
        if (!Strings.isNullOrEmpty(resourceType)) {
            resourceTypeEnum = SystemResource.ResourceType.valueOf(resourceType);
        }

        Boolean disabledFlag = null;
        if (!Strings.isNullOrEmpty(disabled)) {
            disabledFlag = "true".equals(disabled);
        }

        return resourceService.paginate(searchText, resourceTypeEnum, disabledFlag, pageable, null);
    }

    /**
     * 资源详情页面
     */
    @GetMapping("/system/mgmt/resources/details")
    public String details(Model model, @RequestParam Long resourceId) {
        SystemResource resource = resourceService.getById(resourceId, null);
        model.addAttribute("resource", resource);
        return "/system/mgmt/resources/details/resource_details.peb.html";
    }

    /**
     * 资源创建页面
     */
    @GetMapping("/system/mgmt/resources/create")
    public String createPage(Model model) {
        // 获取所有可用的父级资源（目录和菜单类型）
        List<SystemResource> parentResources = resourceService.findAllAvailable(null)
                .stream()
                .filter(resource -> resource.resourceType() == SystemResource.ResourceType.FOLDER || 
                                   resource.resourceType() == SystemResource.ResourceType.MENU)
                .toList();
        
        model.addAttribute("parentResources", parentResources);
        return "/system/mgmt/resources/create/resource_create.peb.html";
    }

    /**
     * 资源编辑页面
     */
    @GetMapping("/system/mgmt/resources/edit")
    public String editPage(Model model, @RequestParam Long resourceId) {
        SystemResource resource = resourceService.getById(resourceId, null);
        
        // 获取所有可用的父级资源（排除自己和子级）
        List<SystemResource> parentResources = resourceService.findAllAvailable(null)
                .stream()
                .filter(r -> !resourceId.equals(r.id()))
                .filter(r -> r.resourceType() == SystemResource.ResourceType.FOLDER ||
                            r.resourceType() == SystemResource.ResourceType.MENU)
                .toList();
        
        model.addAttribute("resource", resource);
        model.addAttribute("parentResources", parentResources);
        return "/system/mgmt/resources/edit/resource_edit.peb.html";
    }

    /**
     * 创建资源
     */
    @PostMapping("/api/system/mgmt/resources")
    @ResponseBody
    @RequiresPermission("system:resource:create")
    public Result<?> createResource(@Valid @RequestBody CreateSystemResourceInput input, BindingResult result) {
        if (result.hasErrors()) {
            return Result.error(result);
        }

        SystemResource resource = resourceService.createResource(input);
        return Result.data(resource);
    }

    /**
     * 更新资源
     */
    @PutMapping("/api/system/mgmt/resources/{resourceId}")
    @ResponseBody
    @RequiresPermission("system:resource:update")
    public Result<?> updateResource(@PathVariable Long resourceId,
                                   @Valid @RequestBody UpdateSystemResourceInput input,
                                   BindingResult result) {
        if (result.hasErrors()) {
            return Result.error(result);
        }

        // 确保ID一致
        input.setId(resourceId);
        SystemResource resource = resourceService.updateResource(input);
        return Result.data(resource);
    }

    /**
     * 删除资源
     */
    @DeleteMapping("/api/system/mgmt/resources/{resourceId}")
    @ResponseBody
    @RequiresPermission("system:resource:delete")
    public Result<?> deleteResource(@PathVariable Long resourceId) {
        boolean success = resourceService.deleteResource(resourceId);
        if (success) {
            return Result.ok("删除资源成功");
        } else {
            return Result.error("删除资源失败");
        }
    }

    /**
     * 获取资源详情
     */
    @GetMapping("/api/system/mgmt/resources/{resourceId}")
    @ResponseBody
    public Result<?> getResourceDetails(@PathVariable Long resourceId) {
        SystemResource resource = resourceService.getById(resourceId, null);
        return Result.data(resource);
    }

    /**
     * 获取资源列表API
     */
    @GetMapping("/api/system/mgmt/resources")
    @ResponseBody
    public Result<?> getResourcesList(@RequestParam(required = false) String disabled,
                                      @RequestParam(required = false) String resourceType,
                                      @RequestParam(required = false) String searchText,
                                      Pageable pageable) {
        SystemResource.ResourceType resourceTypeEnum = null;
        if (!Strings.isNullOrEmpty(resourceType)) {
            resourceTypeEnum = SystemResource.ResourceType.valueOf(resourceType);
        }

        Boolean disabledFlag = null;
        if (!Strings.isNullOrEmpty(disabled)) {
            disabledFlag = "true".equals(disabled);
        }

        Page<SystemResource> pagedResources = resourceService.paginate(searchText, resourceTypeEnum, disabledFlag, pageable, null);
        return Result.data(pagedResources);
    }

    /**
     * 获取所有可用资源列表
     */
    @GetMapping("/api/system/mgmt/resources/available")
    @ResponseBody
    public Result<?> getAvailableResources() {
        List<SystemResource> resources = resourceService.findAllAvailable(null);
        return Result.data(resources);
    }



    /**
     * 获取资源树结构
     */
    @GetMapping("/api/system/mgmt/resources/tree")
    @ResponseBody
    public Result<?> getResourceTree() {
        try {
            List<SystemResource> allResources = resourceService.findAllAvailable(null);
            List<ResourceTreeNode> resourceTree = buildResourceTreeNodes(allResources);
            return Result.data(resourceTree);
        } catch (Exception ex) {
            log.error("获取资源树失败", ex);
            return Result.error("获取资源树失败: " + ex.getMessage());
        }
    }

    /**
     * 构建资源树节点
     */
    private List<ResourceTreeNode> buildResourceTreeNodes(List<SystemResource> allResources) {
        Map<Long, List<SystemResource>> parentChildMap = new HashMap<>();
        List<SystemResource> rootResources = new ArrayList<>();

        // 按父级ID分组
        for (SystemResource resource : allResources) {
            if (resource.parentId() == null) {
                rootResources.add(resource);
            } else {
                parentChildMap.computeIfAbsent(resource.parentId(), k -> new ArrayList<>()).add(resource);
            }
        }

        // 递归构建树状结构
        return rootResources.stream()
                .map(resource -> buildTreeNode(resource, parentChildMap))
                .collect(Collectors.toList());
    }

    /**
     * 递归构建树节点
     */
    private ResourceTreeNode buildTreeNode(SystemResource resource, Map<Long, List<SystemResource>> parentChildMap) {
        List<SystemResource> children = parentChildMap.getOrDefault(resource.id(), Collections.emptyList());
        List<ResourceTreeNode> childNodes = children.stream()
                .map(child -> buildTreeNode(child, parentChildMap))
                .collect(Collectors.toList());

        return new ResourceTreeNode(
                resource.id(),
                resource.name(),
                resource.displayName(),
                resource.parentId(),
                resource.sortNum(),
                resource.permission(),
                resource.url(),
                resource.target(),
                resource.resourceType(),
                resource.disabled(),
                resource.hidden(),
                resource.autoRefresh(),
                resource.icon(),
                resource.remark(),
                childNodes
        );
    }

    /**
     * 资源树节点DTO
     */
    public static class ResourceTreeNode {
        public final Long id;
        public final String name;
        public final String displayName;
        public final Long parentId;
        public final int sortNum;
        public final String permission;
        public final String url;
        public final String target;
        public final SystemResource.ResourceType resourceType;
        public final boolean disabled;
        public final boolean hidden;
        public final boolean autoRefresh;
        public final String icon;
        public final String remark;
        public final List<ResourceTreeNode> children;

        public ResourceTreeNode(Long id, String name, String displayName, Long parentId, int sortNum,
                                String permission, String url, String target, SystemResource.ResourceType resourceType,
                                boolean disabled, boolean hidden, boolean autoRefresh, String icon, String remark,
                                List<ResourceTreeNode> children) {
            this.id = id;
            this.name = name;
            this.displayName = displayName;
            this.parentId = parentId;
            this.sortNum = sortNum;
            this.permission = permission;
            this.url = url;
            this.target = target;
            this.resourceType = resourceType;
            this.disabled = disabled;
            this.hidden = hidden;
            this.autoRefresh = autoRefresh;
            this.icon = icon;
            this.remark = remark;
            this.children = children;
        }
    }

    /**
     * 权限编码唯一性检查
     */
    @GetMapping("/api/system/mgmt/resources/check-permission")
    @ResponseBody
    public Result<?> checkPermission(@RequestParam String permission, @RequestParam(required = false) Long excludeId) {
        boolean exists;

        if (excludeId != null) {
            exists = resourceService.existsByPermissionAndIdNot(permission, excludeId);
        } else {
            exists = resourceService.existsByPermission(permission);
        }
        return Result.data(!exists);
    }

    /**
     * 导出资源数据
     */
    @GetMapping("/api/system/mgmt/resources/export")
    public void exportResources(@RequestParam(required = false) List<Long> ids,
                                HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Disposition", "attachment; filename=resources_" +
                System.currentTimeMillis() + ".csv");

        // 获取要导出的资源
        List<SystemResource> resources;
        if (ids != null && !ids.isEmpty()) {
            resources = new ArrayList<>();
            for (Long id : ids) {
                resources.add(resourceService.getById(id, null));
            }
        } else {
            // 导出所有资源
            resources = resourceService.findAllAvailable(null);
        }

        // 写入CSV数据
        PrintWriter writer = response.getWriter();
        writer.println("ID,资源名称,显示名称,权限编码,资源类型,URL,状态,排序号,备注,创建时间");

        for (SystemResource resource : resources) {
            writer.printf("%d,%s,%s,%s,%s,%s,%s,%d,%s,%s%n",
                    resource.id(),
                    resource.name(),
                    resource.displayName(),
                    resource.permission(),
                    resource.resourceType().displayName(),
                    resource.url(),
                    resource.disabled() ? "禁用" : "启用",
                    resource.sortNum(),
                    resource.remark() != null ? resource.remark() : "",
                    resource.createTime() != null ? resource.createTime().toString() : ""
            );
        }

        writer.flush();
    }
}
