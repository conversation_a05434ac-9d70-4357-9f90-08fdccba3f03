package admiz.system.controller.mgmt;

import admiz.auth.AuthContext;
import admiz.common.auth.RequiresPermission;
import admiz.common.errorhandling.AppErrors;
import admiz.common.result.Result;
import admiz.system.model.Fetchers;
import admiz.system.model.SystemRole;
import admiz.system.model.SystemUser;
import admiz.system.model.dto.*;
import admiz.system.repository.SystemUserRepository;
import admiz.system.service.*;
import admiz.utils.FormSort;
import com.google.common.base.Strings;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.babyfish.jimmer.sql.ast.mutation.SaveMode;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller  @Slf4j @RequiredArgsConstructor
@RequiresPermission("system:user:view")
public class UserManagementController {
    final SystemUserRepository userRepository;
    final SystemUserService userService;
    final SystemUserRoleService userRoleService;
    final SystemRoleService roleService;
    final SystemSecurityService securityService;
    final SystemPermissionAuditService auditService;

    public record UserIndexVo(String searchText, String locked, String sort){}

    @GetMapping("/system/mgmt/users")
    public String index(Model model, UserIndexVo userIndexVo, Pageable pageable) {
        Page<SystemUser> pagedUsers = findUsers(userIndexVo.searchText, userIndexVo.locked, pageable);

        model.addAttribute("page", pagedUsers);
        model.addAttribute("sort", FormSort.of(pageable.getSort()));
        model.addAttribute("userIndexVo", userIndexVo);

        return "/system/mgmt/users/index/index.peb.html";
    }

    private Page<SystemUser> findUsers(String searchText, String locked, Pageable pageable) {
        log.debug("查询用户列表: searchText={}, locked={}, page={}, size={}",
                searchText, locked, pageable.getPageNumber(), pageable.getPageSize());

            // 加载用户和角色信息
            Page<SystemUser> result = userRepository.paginate(searchText, locked, pageable,
                Fetchers.SYSTEM_USER_FETCHER
                    .allScalarFields()
                    .roles(Fetchers.SYSTEM_ROLE_FETCHER.allScalarFields())
            );

            log.debug("用户列表查询成功: totalElements={}, numberOfElements={}",
                    result.getTotalElements(), result.getNumberOfElements());

            return result;
    }


    @GetMapping("/system/mgmt/users/details/account")
    public String details_account(Model model, @RequestParam Long userId) {
        SystemUser user = userRepository.findById(userId).orElseThrow(AppErrors.CommonError.NOT_FOUND::toException);
        model.addAttribute("user", user);
        return "/system/mgmt/users/details/user_details_account_info.peb.html";
    }

    @GetMapping("/system/mgmt/users/details/profile")
    public String details_profile(Model model, @RequestParam Long userId) {
        SystemUser user = userRepository.findById(userId).orElseThrow(AppErrors.CommonError.NOT_FOUND::toException);
        model.addAttribute("user", user);
        return "/system/mgmt/users/details/user_details_profile.peb.html";
    }

    @GetMapping("/system/mgmt/users/details/activity")
    public String details_activity(Model model, @RequestParam Long userId) {
        SystemUser user = userRepository.findById(userId).orElseThrow(AppErrors.CommonError.NOT_FOUND::toException);
        model.addAttribute("user", user);
        return "/system/mgmt/users/details/user_details_activity.peb.html";
    }

    public record UpdateAccountInfoForm(@NotBlank(message = "用户名不能为空") String login, String mobile, String email, String password, String passwordConfirm) {}

    @SneakyThrows
    @PostMapping("/system/mgmt/users/details/account/save") @ResponseBody
    public Result<?> updateUser(@Valid @RequestBody UpdateSystemUserInput updateInput, BindingResult result) {
        if (result.hasErrors()) {
            return Result.error(result);
        }

        if(!Strings.isNullOrEmpty(updateInput.getPassword()) && !updateInput.getPassword().equals(updateInput.getPasswordConfirm())) {
            return Result.fail("两次输入的密码不一致");
        }

        SystemUser user = userRepository.findById(updateInput.getId()).orElseThrow(AppErrors.Auth.ACCOUNT_NOT_FOUND::toException);

        Result<?> error = preventLockoutBuiltinAdmin(user, updateInput);
        if(error != null) {
            return error;
        }

        if(!Strings.isNullOrEmpty(updateInput.getPassword())) {
            securityService.changePassword(updateInput.getId(), updateInput.getPassword());
        }

        if(updateInput.isLocked() && updateInput.getLockedUntil() != null && updateInput.getLockedUntil().isBefore(LocalDateTime.now())) {
            return Result.fail("锁定时间不能早于现在");
        }

        SystemUser systemUser = userRepository.save(updateInput, SaveMode.UPDATE_ONLY);

        // 记录用户管理操作日志
        try {
            Long operatorUserId = AuthContext.currentUser().id();
            String operatorUserLogin = AuthContext.currentUser().login();

            String operationType = "USER_UPDATE";
            String operationDescription = "更新用户信息: " + user.login();

            // 检查是否是禁用/启用操作
            if (user.locked() != updateInput.isLocked()) {
                if (updateInput.isLocked()) {
                    operationType = "USER_DISABLE";
                    operationDescription = "禁用用户: " + user.login();
                } else {
                    operationType = "USER_ENABLE";
                    operationDescription = "启用用户: " + user.login();
                }
            }

            auditService.logUserManagement(operatorUserId, operatorUserLogin,
                    user.id(), user.login(), operationType, operationDescription, true, null);
        } catch (Exception auditEx) {
            log.warn("记录用户管理操作审计日志失败", auditEx);
        }

        return Result.ok();
    }

    private Result<?> preventLockoutBuiltinAdmin(SystemUser user, @Valid UpdateSystemUserInput updateInput) {
        if(!user.isBuiltinAdmin()) {
            return null;
        }

        if(updateInput.isLocked()) {
            return Result.fail("不能禁用系统内置管理员");
        }

        if(!Strings.isNullOrEmpty(updateInput.getPassword())) {
            return Result.fail("系统内置管理员不能通过该方法修改密码");
        }

        return null;
    }

    @GetMapping("/system/mgmt/users/details/roles")
    public String userRolesPage(Model model, @RequestParam Long userId) {
        SystemUser user = userRepository.findById(userId)
                .orElseThrow(AppErrors.CommonError.NOT_FOUND::toException);
        List<SystemRole> userRoles = userRoleService.getUserRoles(userId);
        List<SystemRole> allRoles = roleService.findAllAvailable(null);

        model.addAttribute("user", user);
        model.addAttribute("userRoles", userRoles);
        model.addAttribute("allRoles", allRoles);

        return "/system/mgmt/users/details/user_details_role_assignment.peb.html";
    }

    @GetMapping("/api/system/mgmt/users/roles")
    @ResponseBody
    public Result<?> getUserRoles(@RequestParam Long userId) {
        try {
            List<SystemRole> roles = userRoleService.getUserRoles(userId);
            return Result.data(roles);
        } catch (Exception ex) {
            log.error("获取用户角色失败: userId={}", userId, ex);
            return Result.error("获取用户角色失败: " + ex.getMessage());
        }
    }

    @PostMapping("/api/system/mgmt/users/roles")
    @ResponseBody
    public Result<?> assignUserRoles(@RequestParam Long userId,
                                   @Valid @RequestBody AssignUserRolesInput input,
                                   BindingResult result) {
        if (result.hasErrors()) {
            return Result.error(result);
        }

        if (!userId.equals(input.getUserId())) {
            return Result.error("用户ID不匹配");
        }

        try {
            boolean success = userRoleService.assignRoles(userId, input.getRoleIds());

            if (success) {
                return Result.ok("角色分配成功");
            } else {
                return Result.error("角色分配失败");
            }
        } catch (Exception ex) {
            log.error("用户角色分配失败: userId={}", userId, ex);
            return Result.error("角色分配失败: " + ex.getMessage());
        }
    }

    @DeleteMapping("/api/system/mgmt/users/roles")
    @ResponseBody
    public Result<?> removeUserRoles(@RequestParam Long userId,
                                   @Valid @RequestBody RemoveUserRolesInput input,
                                   BindingResult result) {
        if (result.hasErrors()) {
            return Result.error(result);
        }

        if (!userId.equals(input.getUserId())) {
            return Result.error("用户ID不匹配");
        }

        try {
            boolean success = userRoleService.removeRoles(userId, input.getRoleIds());

            if (success) {
                return Result.ok("角色移除成功");
            } else {
                return Result.error("角色移除失败");
            }
        } catch (Exception ex) {
            log.error("用户角色移除失败: userId={}", userId, ex);
            return Result.error("角色移除失败: " + ex.getMessage());
        }
    }

    @PostMapping("/api/system/mgmt/users/batch-assign-roles")
    @ResponseBody
    public Result<?> batchAssignUserRoles(@Valid @RequestBody BatchAssignUserRolesInput input,
                                        BindingResult result) {
        if (result.hasErrors()) {
            return Result.error(result);
        }

        if (input.getUserIds().isEmpty()) {
            return Result.fail("请选择要操作的用户");
        }

        try {
            boolean success = userRoleService.batchAssignRoles(input.getUserIds(), input.getRoleIds());

            if (success) {
                return Result.ok("批量角色分配成功");
            } else {
                return Result.ok("批量角色分配完成，部分用户分配失败");
            }
        } catch (Exception ex) {
            log.error("批量用户角色分配失败", ex);
            return Result.error("批量角色分配失败: " + ex.getMessage());
        }
    }

    @PostMapping("/api/system/mgmt/users/batch-remove-roles")
    @ResponseBody
    public Result<?> batchRemoveUserRoles(@Valid @RequestBody BatchRemoveUserRolesInput input,
                                        BindingResult result) {
        if (result.hasErrors()) {
            return Result.error(result);
        }

        if (input.getUserIds().isEmpty()) {
            return Result.fail("请选择要操作的用户");
        }

        try {
            boolean success = userRoleService.batchRemoveRoles(input.getUserIds(), input.getRoleIds());

            if (success) {
                return Result.ok("批量角色移除成功");
            } else {
                return Result.ok("批量角色移除完成，部分用户移除失败");
            }
        } catch (Exception ex) {
            log.error("批量用户角色移除失败", ex);
            return Result.error("批量角色移除失败: " + ex.getMessage());
        }
    }

    @PostMapping("/api/system/mgmt/users/batch-add-roles")
    @ResponseBody
    public Result<?> batchAddUserRoles(@Valid @RequestBody BatchAddUserRolesInput input,
                                     BindingResult result) {
        if (result.hasErrors()) {
            return Result.error(result);
        }

        if (input.getUserIds().isEmpty()) {
            return Result.fail("请选择要操作的用户");
        }

        try {
            boolean success = userRoleService.batchAddRoles(input.getUserIds(), input.getRoleIds());

            if (success) {
                return Result.ok("批量角色添加成功");
            } else {
                return Result.ok("批量角色添加完成，部分用户添加失败");
            }
        } catch (Exception ex) {
            log.error("批量用户角色添加失败", ex);
            return Result.error("批量角色添加失败: " + ex.getMessage());
        }
    }

    @GetMapping("/api/system/mgmt/users/batch-roles-info")
    @ResponseBody
    public Result<?> getBatchUsersRolesInfo(@RequestParam List<Long> userIds) {
        try {
            Map<Long, List<SystemRole>> userRolesMap = new HashMap<>();

            for (Long userId : userIds) {
                List<SystemRole> roles = userRoleService.getUserRoles(userId);
                userRolesMap.put(userId, roles);
            }

            return Result.data(userRolesMap);
        } catch (Exception ex) {
            log.error("获取批量用户角色信息失败", ex);
            return Result.error("获取用户角色信息失败: " + ex.getMessage());
        }
    }
}
