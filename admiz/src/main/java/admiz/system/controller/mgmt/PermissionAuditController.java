package admiz.system.controller.mgmt;

import admiz.common.auth.RequiresPermission;
import admiz.common.result.Result;
import admiz.system.model.SystemPermissionLog;
import admiz.system.service.SystemPermissionAuditService;
import admiz.utils.FormSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

// 权限审计日志管理控制器
@Controller
@Slf4j
@RequiredArgsConstructor
@RequiresPermission("system:audit:view")
public class PermissionAuditController {

    private final SystemPermissionAuditService auditService;

    // 权限审计日志列表页面
    @GetMapping("/system/mgmt/audit/logs")
    public String index(Model model, AuditLogIndexVo auditLogIndexVo, Pageable pageable) {
        log.debug("权限审计日志查询开始 - operationType: {}, operationResult: {}, pageable: {}", auditLogIndexVo.operationType, auditLogIndexVo.operationResult, pageable);

        Page<SystemPermissionLog> pagedLogs = auditService.queryLogs(auditLogIndexVo.operationType, auditLogIndexVo.operationResult, auditLogIndexVo.recordStatus, auditLogIndexVo.operatorUserId, auditLogIndexVo.targetUserId, auditLogIndexVo.startTime, auditLogIndexVo.endTime, pageable);

        model.addAttribute("page", pagedLogs);
        model.addAttribute("sort", FormSort.of(pageable.getSort()));
        model.addAttribute("auditLogIndexVo", auditLogIndexVo);

        // 添加操作类型选项
        model.addAttribute("operationTypes", SystemPermissionLog.OperationType.values());
        model.addAttribute("operationResults", SystemPermissionLog.OperationResult.values());
        model.addAttribute("recordStatuses", SystemPermissionLog.RecordStatus.values());

        log.debug("权限审计日志查询结果 - 总记录数: {}, 当前页记录数: {}", pagedLogs.getTotalElements(), pagedLogs.getNumberOfElements());

        return "/system/mgmt/audit/logs/index.peb.html";
    }

    // 获取权限审计日志列表API
    @GetMapping("/api/system/mgmt/audit/logs")
    @ResponseBody
    public Result<Page<SystemPermissionLog>> getAuditLogsList(@RequestParam(required = false) String operationType, @RequestParam(required = false) String operationResult, @RequestParam(required = false) String recordStatus, @RequestParam(required = false) Long operatorUserId, @RequestParam(required = false) Long targetUserId, @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime, @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime, Pageable pageable) {
        Page<SystemPermissionLog> pagedLogs = auditService.queryLogs(operationType, operationResult, recordStatus, operatorUserId, targetUserId, startTime, endTime, pageable);
        return Result.data(pagedLogs);

    }

    // 权限审计概览页面
    @GetMapping("/system/mgmt/audit/overview")
    public String overview(Model model, @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime, @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {

        // 如果没有指定时间范围，默认查询最近7天
        if (startTime == null) {
            startTime = LocalDateTime.now().minusDays(7);
        }
        if (endTime == null) {
            endTime = LocalDateTime.now();
        }

        Map<String, Object> overview = auditService.getAuditOverview(startTime, endTime);
        model.addAttribute("overview", overview);
        model.addAttribute("startTime", startTime);
        model.addAttribute("endTime", endTime);

        return "/system/mgmt/audit/overview/index.peb.html";
    }

    // 用户操作日志页面
    @GetMapping("/system/mgmt/audit/user/{userId}")
    public String userLogs(Model model, @PathVariable Long userId) {
        List<SystemPermissionLog> operationLogs = auditService.getUserOperationLogs(userId, 100);
        List<SystemPermissionLog> targetLogs = auditService.getUserTargetLogs(userId, 100);

        model.addAttribute("userId", userId);
        model.addAttribute("operationLogs", operationLogs);
        model.addAttribute("targetLogs", targetLogs);

        return "/system/mgmt/audit/user/logs.peb.html";
    }

    // 权限审计日志查询参数
    public record AuditLogIndexVo(String operationType, String operationResult, String recordStatus,
                                  Long operatorUserId, Long targetUserId,
                                  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
                                  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime, String sort) {
    }

    // 获取权限审计概览API
    @GetMapping("/api/system/mgmt/audit/overview")
    @ResponseBody
    public Result<Map<String, Object>> getAuditOverview(@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime, @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {

        if (startTime == null) {
            startTime = LocalDateTime.now().minusDays(7);
        }
        if (endTime == null) {
            endTime = LocalDateTime.now();
        }

        Map<String, Object> overview = auditService.getAuditOverview(startTime, endTime);
        return Result.data(overview);
    }

    // 获取操作类型统计API
    @GetMapping("/api/system/mgmt/audit/statistics/operation-types")
    @ResponseBody
    public Result<Map<String, Long>> getOperationTypeStatistics(@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime, @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {

        Map<String, Long> statistics = auditService.getOperationTypeStatistics(startTime, endTime);
        return Result.data(statistics);

    }

    // 获取用户操作排行API
    @GetMapping("/api/system/mgmt/audit/statistics/user-ranking")
    @ResponseBody
    public Result<List<Map<String, Object>>> getUserOperationRanking(@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime, @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime, @RequestParam(defaultValue = "10") int limit) {

        List<Map<String, Object>> ranking = auditService.getUserOperationRanking(startTime, endTime, limit);
        return Result.data(ranking);
    }

    // 获取用户操作日志API
    @GetMapping("/api/system/mgmt/audit/user/{userId}/operations")
    @ResponseBody
    public Result<List<SystemPermissionLog>> getUserOperationLogs(@PathVariable Long userId, @RequestParam(defaultValue = "50") int limit) {

        List<SystemPermissionLog> logs = auditService.getUserOperationLogs(userId, limit);
        return Result.data(logs);

    }

    // 获取针对用户的操作日志API
    @GetMapping("/api/system/mgmt/audit/user/{userId}/targets")
    @ResponseBody
    public Result<List<SystemPermissionLog>> getUserTargetLogs(@PathVariable Long userId, @RequestParam(defaultValue = "50") int limit) {

        List<SystemPermissionLog> logs = auditService.getUserTargetLogs(userId, limit);
        return Result.data(logs);
    }

    // 获取失败操作列表API
    @GetMapping("/api/system/mgmt/audit/failed-operations")
    @ResponseBody
    public Result<List<SystemPermissionLog>> getFailedOperations(@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime since, @RequestParam(defaultValue = "50") int limit) {

        List<SystemPermissionLog> logs = auditService.getFailedOperations(since, limit);
        return Result.data(logs);
    }

    // 获取最近权限拒绝记录API
    @GetMapping("/api/system/mgmt/audit/user/{userId}/permission-denied")
    @ResponseBody
    public Result<List<SystemPermissionLog>> getRecentPermissionDenied(@PathVariable Long userId, @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime since) {

        if (since == null) {
            since = LocalDateTime.now().minusDays(1); // 默认查询最近1天
        }

        List<SystemPermissionLog> logs = auditService.getRecentPermissionDenied(userId, since);
        return Result.data(logs);
    }

    // 清理过期日志API
    @PostMapping("/api/system/mgmt/audit/cleanup")
    @ResponseBody
    @RequiresPermission("system:audit:cleanup")
    public Result<Integer> cleanupExpiredLogs(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime beforeTime) {

        int deletedCount = auditService.cleanupExpiredLogs(beforeTime);
        return Result.data(deletedCount);
    }
}
