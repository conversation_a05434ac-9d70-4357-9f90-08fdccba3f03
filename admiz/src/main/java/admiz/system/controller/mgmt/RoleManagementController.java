package admiz.system.controller.mgmt;

import admiz.common.auth.RequiresPermission;
import admiz.common.result.Result;
import admiz.system.model.SystemResource;
import admiz.system.model.SystemRole;
import admiz.system.model.dto.BatchOperationResult;
import admiz.system.model.dto.BatchUpdateRoleStatusInput;
import admiz.system.model.dto.CreateSystemRoleInput;
import admiz.system.model.dto.UpdateSystemRoleInput;
import admiz.system.service.SystemRoleService;
import admiz.utils.FormSort;
import com.google.common.base.Strings;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

/**
 * 角色管理控制器
 * 
 * <AUTHOR>
 */
@Controller
@Slf4j
@RequiredArgsConstructor
@RequiresPermission("system:role:view")
public class RoleManagementController {
    
    private final SystemRoleService roleService;

    /**
     * 角色列表查询参数
     */
    public record RoleIndexVo(String searchText, String disabled, String sort) {}

    /**
     * 角色列表页面
     */
    @GetMapping("/system/mgmt/roles")
    public String index(Model model, RoleIndexVo roleIndexVo, Pageable pageable) {
        log.debug("角色列表查询开始 - searchText: {}, disabled: {}, sort: {}, pageable: {}", 
                 roleIndexVo.searchText, roleIndexVo.disabled, roleIndexVo.sort, pageable);
        
        Page<SystemRole> pagedRoles = findRoles(roleIndexVo.searchText, roleIndexVo.disabled, pageable);
        
        log.debug("角色列表查询结果 - 总记录数: {}, 当前页记录数: {}, 总页数: {}, 当前页: {}", 
                 pagedRoles.getTotalElements(), pagedRoles.getNumberOfElements(), 
                 pagedRoles.getTotalPages(), pagedRoles.getNumber());
        
        // 打印查询到的角色信息
        if (log.isDebugEnabled()) {
            pagedRoles.getContent().forEach(role -> 
                log.debug("查询到角色 - id: {}, name: {}, displayName: {}, disabled: {}, deleted: {}", 
                         role.id(), role.name(), role.displayName(), role.disabled(), role.deleted()));
        }

        model.addAttribute("page", pagedRoles);
        model.addAttribute("sort", FormSort.of(pageable.getSort()));
        model.addAttribute("roleIndexVo", roleIndexVo);

        return "/system/mgmt/roles/index/index.peb.html";
    }

    /**
     * 分页查询角色列表
     */
    private Page<SystemRole> findRoles(String searchText, String disabled, Pageable pageable) {
        log.debug("findRoles方法调用 - searchText: '{}', disabled: '{}', pageable: {}", searchText, disabled, pageable);
        
        Boolean disabledFlag = null;
        if (!Strings.isNullOrEmpty(disabled)) {
            disabledFlag = "true".equals(disabled);
            log.debug("转换disabled标志 - 原始值: '{}', 转换后: {}", disabled, disabledFlag);
        }
        
        log.debug("调用roleService.paginate - searchText: '{}', disabledFlag: {}, pageable: {}", 
                 searchText, disabledFlag, pageable);
        
        Page<SystemRole> result = roleService.paginate(searchText, disabledFlag, pageable, null);
        
        log.debug("roleService.paginate返回结果 - 总记录数: {}, 当前页记录数: {}", 
                 result.getTotalElements(), result.getNumberOfElements());
        
        return result;
    }

    /**
     * 角色详情页面
     */
    @GetMapping("/system/mgmt/roles/details")
    public String details(Model model, @RequestParam Long roleId) {
        SystemRole role = roleService.getById(roleId, null);
        model.addAttribute("role", role);
        return "/system/mgmt/roles/details/role_details.peb.html";
    }

    /**
     * 角色创建页面
     */
    @GetMapping("/system/mgmt/roles/create")
    public String createPage(Model model) {
        return "/system/mgmt/roles/create/role_create.peb.html";
    }

    /**
     * 角色编辑页面
     */
    @GetMapping("/system/mgmt/roles/edit")
    public String editPage(Model model, @RequestParam Long roleId) {
        SystemRole role = roleService.getById(roleId, null);
        model.addAttribute("role", role);
        return "/system/mgmt/roles/edit/role_edit.peb.html";
    }

    /**
     * 创建角色
     */
    @PostMapping("/api/system/mgmt/roles")
    @ResponseBody
    public Result<?> createRole(@Valid @RequestBody CreateSystemRoleInput input, BindingResult result) {
        if (result.hasErrors()) {
            return Result.error(result);
        }

        SystemRole role = roleService.createRole(input);
        return Result.data(role);
    }

    /**
     * 更新角色
     */
    @PutMapping("/api/system/mgmt/roles/{roleId}")
    @ResponseBody
    public Result<?> updateRole(@PathVariable Long roleId, 
                               @Valid @RequestBody UpdateSystemRoleInput input, 
                               BindingResult result) {
        if (result.hasErrors()) {
            return Result.error(result);
        }

        // 确保ID一致
        input.setId(roleId);


        SystemRole role = roleService.updateRole(input);
        return Result.data(role);
    }

    /**
     * 删除角色
     */
    @DeleteMapping("/api/system/mgmt/roles/{roleId}")
    @ResponseBody
    public Result<?> deleteRole(@PathVariable Long roleId) {

            boolean success = roleService.deleteRole(roleId);
            if (success) {
                return Result.ok("删除角色成功");
            } else {
                return Result.error("删除角色失败");
            }
    }

    /**
     * 获取角色详情
     */
    @GetMapping("/api/system/mgmt/roles/{roleId}")
    @ResponseBody
    public Result<?> getRoleDetails(@PathVariable Long roleId) {
            SystemRole role = roleService.getById(roleId, null);
            return Result.data(role);
    }

    /**
     * 获取角色列表API
     */
    @GetMapping("/api/system/mgmt/roles")
    @ResponseBody
    public Result<?> getRolesList(@RequestParam(required = false) String disabled,
                                 @RequestParam(required = false) String searchText,
                                 Pageable pageable) {
            Boolean disabledFlag = null;
            if (!Strings.isNullOrEmpty(disabled)) {
                disabledFlag = "true".equals(disabled);
            }

            Page<SystemRole> pagedRoles = roleService.paginate(searchText, disabledFlag, pageable, null);
            return Result.data(pagedRoles);
    }

    /**
     * 获取所有可用角色列表
     */
    @GetMapping("/api/system/mgmt/roles/available")
    @ResponseBody
    public Result<?> getAvailableRoles() {
            List<SystemRole> roles = roleService.findAllAvailable(null);
            return Result.data(roles);
    }

    /**
     * 角色权限分配页面
     */
    @GetMapping("/system/mgmt/roles/{roleId}/permissions")
    public String permissionsPage(Model model, @PathVariable Long roleId) {
        SystemRole role = roleService.getById(roleId, null);
        List<SystemResource> rolePermissions = roleService.getRolePermissions(roleId);
        
        model.addAttribute("role", role);
        model.addAttribute("rolePermissions", rolePermissions);
        
        return "/system/mgmt/roles/permissions/role_permissions.peb.html";
    }

    /**
     * 角色权限分配表单
     */
    public record AssignPermissionsForm(@NotNull Long roleId, List<Long> resourceIds) {}

    /**
     * 分配角色权限
     */
    @PostMapping("/api/system/mgmt/roles/{roleId}/permissions")
    @ResponseBody
    public Result<?> assignPermissions(@PathVariable Long roleId, 
                                     @Valid @RequestBody AssignPermissionsForm form, 
                                     BindingResult result) {
        if (result.hasErrors()) {
            return Result.error(result);
        }

        // 确保ID一致
        if (!roleId.equals(form.roleId)) {
            return Result.error("角色ID不匹配");
        }

            List<Long> resourceIds = form.resourceIds != null ? form.resourceIds : List.of();
            boolean success = roleService.assignPermissions(roleId, resourceIds);
            
            if (success) {
                return Result.ok("权限分配成功");
            } else {
                return Result.error("权限分配失败");
            }
    }

    /**
     * 获取角色权限列表
     */
    @GetMapping("/api/system/mgmt/roles/{roleId}/permissions")
    @ResponseBody
    public Result<?> getRolePermissions(@PathVariable Long roleId) {

            List<SystemResource> permissions = roleService.getRolePermissions(roleId);
            return Result.data(permissions);
    }

    /**
     * 角色名称唯一性检查
     */
    @GetMapping("/api/system/mgmt/roles/check-name")
    @ResponseBody
    public Result<?> checkRoleName(@RequestParam String name, @RequestParam(required = false) Long excludeId) {

            boolean exists;
            
            if (excludeId != null) {
                // 编辑模式：检查名称是否被其他角色使用
                exists = roleService.existsByNameAndIdNot(name, excludeId);
            } else {
                // 新增模式：检查名称是否存在
                exists = roleService.existsByName(name);
            }
            
            return Result.data(!exists); // 返回是否可用（不存在）
    }

    /**
     * 批量更新角色状态
     */
    @PutMapping("/api/system/mgmt/roles/batch-status")
    @ResponseBody
    public Result<?> batchUpdateRoleStatus(@Valid @RequestBody BatchUpdateRoleStatusInput input,
                                           BindingResult result) {
        if (result.hasErrors()) {
            return Result.error(result);
        }

        if (input.getRoleIds().isEmpty()) {
            return Result.error("请选择要操作的角色");
        }

        BatchOperationResult batchResult = roleService.batchUpdateRoleStatus(
                input.getRoleIds(), input.getDisabled());

        if (batchResult.isAllSuccess()) {
            return Result.ok(batchResult.getSummaryMessage());
        } else if (batchResult.isAllFailed()) {
            return Result.error("批量操作全部失败");
        } else {
            return Result.ok(batchResult.getSummaryMessage()); // 部分成功
        }
    }

    /**
     * 批量删除角色
     */
    @DeleteMapping("/api/system/mgmt/roles/batch-delete")
    @ResponseBody
    public Result<?> batchDeleteRoles(@RequestBody List<Long> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return Result.fail("请选择要删除的角色");
        }
        
        try {
            int successCount = 0;
            int failCount = 0;
            
            for (Long roleId : roleIds) {
                try {
                    boolean success = roleService.deleteRole(roleId);
                    if (success) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (Exception e) {
                    log.warn("批量删除角色失败: roleId={}", roleId, e);
                    failCount++;
                }
            }
            
            String message = String.format("批量删除完成：成功 %d 个，失败 %d 个", successCount, failCount);
            
            if (failCount == 0) {
                return Result.ok(message);
            } else if (successCount == 0) {
                return Result.fail("批量删除全部失败");
            } else {
                return Result.ok(message);
            }
            
        } catch (Exception ex) {
            log.error("批量删除角色失败", ex);
            return Result.error("批量删除失败: " + ex.getMessage());
        }
    }

    /**
     * 导出角色数据
     */
    @GetMapping("/api/system/mgmt/roles/export")
    public void exportRoles(@RequestParam(required = false) List<Long> ids, 
                           HttpServletResponse response) throws IOException {
            // 设置响应头
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=roles_" + 
                              System.currentTimeMillis() + ".csv");
            
            // 获取要导出的角色
            List<SystemRole> roles;
            if (ids != null && !ids.isEmpty()) {
                roles = new ArrayList<>();
                for (Long id : ids) {
                        roles.add(roleService.getById(id, null));

                }
            } else {
                // 导出所有角色
                roles = roleService.findAllAvailable(null);
            }
            
            // 写入CSV数据
            PrintWriter writer = response.getWriter();
            writer.println("ID,角色名称,显示名称,状态,排序号,备注,创建时间");
            
            for (SystemRole role : roles) {
                writer.printf("%d,%s,%s,%s,%d,%s,%s%n",
                    role.id(),
                    role.name(),
                    role.displayName(),
                    role.disabled() ? "禁用" : "启用",
                    role.sortNum(),
                    role.remark() != null ? role.remark() : "",
                    role.createTime() != null ? role.createTime().toString() : ""
                );
            }
            
            writer.flush();
    }
}