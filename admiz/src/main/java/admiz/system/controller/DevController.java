package admiz.system.controller;

import admiz.common.setting.ApplicationSetting;
import com.google.common.base.Strings;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

@Controller
@Slf4j @RequiredArgsConstructor
public class DevController {
    final ApplicationSetting appSetting;

    @RequestMapping(value = "/dev/render/**", method = RequestMethod.GET)
    public String renderTemplate(HttpServletRequest request, Model model) {
        requireDevEnvironment();

        String requestURL = request.getRequestURL().toString();
        String templateName = requestURL.split("/dev/render/")[1];

        templateName = templateName.endsWith(".peb.html") ? templateName : templateName + ".peb.html";

        return templateName;
    }

    @RequestMapping(value = "/dev/render-tools", method = RequestMethod.GET)
    public String renderTools(HttpServletRequest request, @RequestParam String templateName, Model model) {
        String renderUrl = "about:blank";
        if(!Strings.isNullOrEmpty(templateName)) {
            templateName = templateName.startsWith("/") ? templateName.substring(1) : templateName;
            templateName = templateName.endsWith(".peb.html") ? templateName : templateName + ".peb.html";
            renderUrl = request.getContextPath() + "/dev/render/" + templateName;
        }

        model.addAttribute("renderUrl", renderUrl);

        return "/dev/render-tools.peb.html";
    }

    private void requireDevEnvironment() {
        if(!appSetting.isDevEnvironment()) {
            throw new IllegalStateException("Dev environment not set");
        }
    }

    @GetMapping("/dev/exception")
    public String debug(Model model) {
        requireDevEnvironment();

        Exception exception = new Exception();
        model.addAttribute("exception", exception);
        model.addAttribute("stackTrace", ExceptionUtils.getStackTrace(exception));
        return "/dev/exception.peb.html";
    }
}
