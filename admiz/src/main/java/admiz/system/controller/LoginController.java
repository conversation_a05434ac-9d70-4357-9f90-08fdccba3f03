package admiz.system.controller;

import admiz.auth.AuthContext;
import admiz.common.result.Result;
import admiz.system.model.*;
import admiz.system.repository.SystemResourceRepository;
import admiz.system.repository.SystemRoleRepository;
import admiz.system.service.SystemSecurityService;
import admiz.system.vo.UserInfoVO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresAuthentication;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Controller @RequestMapping
@Slf4j @RequiredArgsConstructor
public class LoginController {
    final SystemSecurityService securityService;
    final SystemRoleRepository roleRepository;
    final SystemResourceRepository resourceRepository;

    @GetMapping("/login")
    public String loginPage(Model model) {
        if(AuthContext.isAuthenticated()) {
            return "redirect:/home";
        }
        return "/login/login.peb.html";
    }

    public record LoginVO(@NotBlank(message = "用户名不能为空") String login, @NotBlank(message = "密码不能为空") String password,
                          @NotBlank(message = "验证码不能为空") String captcha, Boolean rememberMe) {
        @Override
        public Boolean rememberMe() {
            return rememberMe != null && rememberMe;
        }
    }

    @PostMapping("/login")
    public String formLogin(@Valid LoginVO loginVO, BindingResult result) {
        if (result.hasErrors()) {
            return "/login/login.peb.html";
        }

        SystemUser systemUser = securityService.loginByPassword(loginVO.login, loginVO.password, loginVO.captcha, loginVO.rememberMe());

        return "redirect:/home/<USER>";
    }

    @PostMapping("/api/login") @ResponseBody
    public Result<?> apiLogin(@Valid @RequestBody LoginVO loginVO, BindingResult result) {
        if (result.hasErrors()) {
            return Result.error(result);
        }

        SystemUser systemUser = securityService.loginByPassword(loginVO.login, loginVO.password, loginVO.captcha, loginVO.rememberMe());
        UserInfoVO userInfoVO = getUserInfo(systemUser);

        Map.of("user", userInfoVO);

        return Result.data(userInfoVO);
    }

    @PostMapping("/logout")
    public String logout() {
        SecurityUtils.getSubject().logout();
        return "redirect:/login";
    }

    @RequiresAuthentication()
    @PostMapping("/me")  @ResponseBody
    public Result<?> me() {
        SystemUser systemUser = AuthContext.currentUser();
        UserInfoVO userInfoVO = getUserInfo(systemUser);
        return Result.data(userInfoVO);
    }

    private UserInfoVO getUserInfo(SystemUser systemUser) {
        List<String> roleNameList = roleRepository.findUserRoles(systemUser.id()).stream().map(SystemRole::name).toList();
        SystemResourceFetcher fetcher = Fetchers.SYSTEM_RESOURCE_FETCHER
                .name()
                .displayName()
                .parentId()
                .icon()
                .resourceType()
                .sortNum()
                .target()
                .url();

        List<SystemResource> resourceList = resourceRepository.findUserResources(systemUser.id(), fetcher);

        UserInfoVO userInfoVO = new UserInfoVO(systemUser, roleNameList, resourceList);

        return userInfoVO;
    }
}
