package admiz.system.controller;

import admiz.common.utils.ImageUtil;
import admiz.system.repository.SystemResourceRepository;
import admiz.system.repository.SystemRoleRepository;
import admiz.system.service.SystemCaptchaService;
import admiz.system.service.SystemSecurityService;
import jakarta.servlet.ServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.awt.image.BufferedImage;
import java.nio.charset.StandardCharsets;

@RestController @Slf4j @RequiredArgsConstructor
public class SecurityController {
    final SystemCaptchaService captchaService;
    final SystemSecurityService securityService;
    final SystemRoleRepository roleRepository;
    final SystemResourceRepository resourceRepository;

    @SneakyThrows
    @GetMapping("/security/captcha")
    public void captchaImage(@RequestParam(defaultValue = "image") String format, ServletResponse response) {
        String sessionId = SecurityUtils.getSubject().getSession().getId().toString();

        BufferedImage captcha = captchaService.createCaptcha(sessionId);
        switch (format==null?"":format.toUpperCase()) {
            case "DATA_URI":
                String data = ImageUtil.toDataURI(captcha);
                response.setContentType("text/plain");
                response.getOutputStream().write(data.getBytes(StandardCharsets.US_ASCII));
            default:
                byte[] bytes = ImageUtil.toByteArray(captcha);
                response.setContentType("image/" + ImageUtil.IMAGE_FORMAT_JPG);
                response.getOutputStream().write(bytes);
        }
    }

}
