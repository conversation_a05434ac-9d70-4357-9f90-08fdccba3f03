// package admiz.system.controller;
//
// import admiz.common.auth.AuthContext;
// import admiz.common.errorhandling.AppErrors;
// import admiz.common.setting.ApplicationSetting;
// import com.google.common.base.Strings;
// import jakarta.servlet.http.HttpServletRequest;
// import lombok.RequiredArgsConstructor;
// import lombok.extern.slf4j.Slf4j;
// import org.apache.commons.lang3.exception.ExceptionUtils;
// import org.springframework.stereotype.Controller;
// import org.springframework.ui.Model;
// import org.springframework.web.bind.annotation.GetMapping;
// import org.springframework.web.bind.annotation.RequestMapping;
// import org.springframework.web.bind.annotation.RequestMethod;
// import org.springframework.web.bind.annotation.RequestParam;
//
// @Controller
// @Slf4j @RequiredArgsConstructor
// public class SharedViewRenderController {
//     final ApplicationSetting appSetting;
//
//     @RequestMapping(value = "/view/**", method = RequestMethod.GET)
//     public String renderTemplate(HttpServletRequest request, Model model) {
//         String requestURL = request.getRequestURL().toString();
//         String templateName = requestURL.split("/view/")[1];
//
//         // if(!AuthContext.isPermitted("")) {
//         //     throw AppErrors.CommonError.FORBIDDEN.toException();
//         // }
//
//         templateName = templateName + ".peb.html";
//
//         return templateName;
//     }
// }
