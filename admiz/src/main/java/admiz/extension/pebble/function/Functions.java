package admiz.extension.pebble.function;

import admiz.auth.AuthContext;
import com.google.common.base.Strings;
import io.pebbletemplates.pebble.error.PebbleException;
import io.pebbletemplates.pebble.extension.Function;
import io.pebbletemplates.pebble.extension.escaper.SafeString;
import io.pebbletemplates.pebble.template.EvaluationContext;
import io.pebbletemplates.pebble.template.PebbleTemplate;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.util.StringUtils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

public class Functions {
    public static String getCurrentRequestPathFromVariable(Object variable) {
        if (variable instanceof HttpServletRequest request ) {
            return request.getRequestURI();
        } else {
            return "/";
        }
    }

    public static class CurrentPathFunction implements Function {
        @Override
        public List<String> getArgumentNames() {
            return null;
        }

        @Override
        public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
            return getCurrentRequestPathFromVariable(context.getVariable("request"));
        }
    }

    public static class IsCurrentPathFunction implements Function {
        @Override
        public List<String> getArgumentNames() {
            return List.of("path");
        }

        @Override
        public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
            String path = (String)args.get("path");
            if(Strings.isNullOrEmpty(path)) {
                return false;
            }

            HttpServletRequest request = (HttpServletRequest)context.getVariable("request");
            String contextPath = request.getContextPath();
            String currentRequestPath = request.getRequestURI();
            return StringUtils.pathEquals(contextPath + path, currentRequestPath);
        }
    }

    public static class CurrentUserFunction implements Function {
        final static Random random = new Random(System.currentTimeMillis());

        @Override
        public List<String> getArgumentNames() {
            return null;
        }

        @Override
        public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
            return AuthContext.currentUser();
        }
    }

    public static class RandomFunction implements Function {
        final static Random random = new Random(System.currentTimeMillis());

        @Override
        public List<String> getArgumentNames() {
            return List.of("min", "max");
        }

        @Override
        public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
            Object min = args.get("min");
            Object max = args.get("max");

            if(min == null) {
                min = Double.MIN_VALUE;
            }
            if(max == null) {
                max = Double.MAX_VALUE;
            }

            if(!(min instanceof Number && max instanceof Number)) {
                throw new PebbleException(null, "Parameter min max must be number.", lineNumber, self.getName());
            }

            // 数字类型不一致，就全当成浮点数处理
            if(min.getClass().isAssignableFrom(Double.class) || max.getClass().isAssignableFrom(Double.class)) {
                double minVal = Math.min(((Number) min).doubleValue(), ((Number) max).doubleValue());
                double maxVal = Math.max(((Number) min).doubleValue(), ((Number) max).doubleValue());
                return random.nextDouble(minVal, maxVal);
            } else {
                // 否则当成整数
                long minVal = Math.min(((Number) min).longValue(), ((Number) max).longValue());
                long maxVal = Math.max(((Number) min).longValue(), ((Number) max).longValue());
                return random.nextLong(minVal, maxVal);
            }

        }
    }

    public static class NowFunction extends AbstractDateTimeFormaterFunction implements Function {
        @Override
        public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
            return formatTemporal(args, "yyyy-MM-dd HH:mm:ss");
        }
    }

    public static class DateFunction extends AbstractDateTimeFormaterFunction implements Function {
        @Override
        public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
            return formatTemporal(args, "yyyy-MM-dd");
        }
    }
    public static class TimeFunction extends AbstractDateTimeFormaterFunction implements Function {
        @Override
        public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
            return formatTemporal(args, "HH:mm:ss");
        }
    }

    public static abstract class AbstractDateTimeFormaterFunction implements Function {
        @Override
        public List<String> getArgumentNames() {
            return List.of("format", "timeZone");
        }

        protected SafeString formatTemporal(Map<String, Object> args, String defaultFormat) {
            String format = (String)args.get("format");
            String timeZone = (String)args.get("timezone");
            Locale locale = Locale.getDefault();

            DateFormat intendedFormat = new SimpleDateFormat(format == null ? defaultFormat : format, locale);
            if (timeZone != null) {
                intendedFormat.setTimeZone(TimeZone.getTimeZone(timeZone));
            }

            return new SafeString(intendedFormat.format(LocalDateTime.now()));
        }
    }
}
