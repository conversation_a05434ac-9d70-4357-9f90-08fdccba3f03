package admiz.extension.pebble.filter;

import io.pebbletemplates.pebble.error.PebbleException;
import io.pebbletemplates.pebble.extension.Filter;
import io.pebbletemplates.pebble.template.EvaluationContext;
import io.pebbletemplates.pebble.template.PebbleTemplate;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.util.Strings;
import org.springframework.context.MessageSource;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

@RequiredArgsConstructor
public class TranslateFilter implements Filter {
    final MessageSource messageSource;

    @Override
    public List<String> getArgumentNames() {
        return null;
    }

    @Override
    public Object apply(Object input, Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) throws PebbleException {
        if (input == null) {
            return null;
        }

        String key = input.toString();
        if (Strings.isEmpty(key)) {
            return "";
        } else if (Strings.isBlank(key)) {
            return key;
        } else {
            Locale locale = context.getLocale();
            List<Object> arguments = extractArguments(args);
            return this.messageSource.getMessage(key, arguments.toArray(), key, locale);
        }
    }

    private List<Object> extractArguments(Map<String, Object> args) {
        int i = 0;

        List<Object> arguments;
        for(arguments = new ArrayList(); args.containsKey(String.valueOf(i)); ++i) {
            Object param = args.get(String.valueOf(i));
            arguments.add(param);
        }

        return arguments;
    }
}
