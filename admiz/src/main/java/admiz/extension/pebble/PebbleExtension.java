package admiz.extension.pebble;

import admiz.common.setting.ApplicationSetting;
import admiz.extension.pebble.filter.JsonEncodeFilter;
import admiz.extension.pebble.filter.TranslateFilter;
import admiz.extension.pebble.function.Functions;
import io.pebbletemplates.pebble.extension.AbstractExtension;
import io.pebbletemplates.pebble.extension.Filter;
import io.pebbletemplates.pebble.extension.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component @Slf4j
public class PebbleExtension extends AbstractExtension {
    private final MessageSource messageSource;
    private final ApplicationSetting appSetting;

    public PebbleExtension(MessageSource messageSource, ApplicationSetting appSetting) {
        this.messageSource = messageSource;
        this.appSetting = appSetting;
        log.info("Pebble Extension");
    }

    public Map<String, Filter> getFilters() {
        Map<String, Filter> filters = new HashMap<>();
        filters.put("trans", new TranslateFilter(messageSource));
        filters.put("json_encode", new JsonEncodeFilter());
        return filters;
    }

    public Map<String, Function> getFunctions() {
        Map<String, Function> functions = new HashMap<>();
        functions.put("now", new Functions.NowFunction());
        functions.put("date", new Functions.DateFunction());
        functions.put("time", new Functions.TimeFunction());
        functions.put("random", new Functions.RandomFunction());
        functions.put("user", new Functions.CurrentUserFunction());
        functions.put("is_current_path", new Functions.IsCurrentPathFunction());
        functions.put("current_path", new Functions.CurrentPathFunction());

        return functions;
    }

    @Override
    public Map<String, Object> getGlobalVariables() {
        Map<String, Object> globalVariables = new HashMap<>();
        globalVariables.put("g_environment", appSetting.getEnvironment());
        globalVariables.put("g_is_dev", appSetting.isDevEnvironment());
        globalVariables.put("g_is_prod", appSetting.isProdEnvironment());
        return globalVariables;
    }
}
