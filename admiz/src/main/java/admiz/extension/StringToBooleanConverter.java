package admiz.extension;

import org.apache.logging.log4j.util.Strings;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

public class StringToBooleanConverter implements Converter<String, Boolean> {
    public Boolean convert(String source) {
        return Strings.isBlank(source) ?
                Boolean.FALSE :
                (!source.equalsIgnoreCase("false"));
    }
}
