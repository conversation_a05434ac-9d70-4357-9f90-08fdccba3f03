package admiz.config;

import admiz.common.setting.ApplicationSetting;
import admiz.module.ModuleManager;
import jakarta.servlet.ServletContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Slf4j
@Configuration
public class ModuleConfiguration {
    @Bean
    public ModuleManager moduleManager(ApplicationSetting appSetting, ServletContext servletContext) {
        String contextPath = servletContext.getContextPath();
        List<String> strings = appSetting.enabledModules();
        return new ModuleManager(strings, contextPath);
    }
}
