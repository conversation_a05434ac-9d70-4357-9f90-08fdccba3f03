package admiz.config;

import admiz.common.i18n.I18n;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationContext;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Component;

@Component @RequiredArgsConstructor
public class StaticContextInitializer {
    final ApplicationContext applicationContext;
    final MessageSource messageSource;

    @PostConstruct
    public void initialize() {
        I18n.setMessageSource(messageSource);
//        AuthContext.initialize(applicationContext);
    }

}
