package admiz.config;

import admiz.common.objectMapper.SimpleObjectMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.pebbletemplates.pebble.PebbleEngine;
import io.pebbletemplates.pebble.lexer.Syntax;
import io.pebbletemplates.pebble.loader.FileLoader;
import io.pebbletemplates.pebble.loader.Loader;
import io.pebbletemplates.spring.servlet.PebbleViewResolver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import org.springframework.web.servlet.i18n.AcceptHeaderLocaleResolver;

import java.io.Reader;
import java.util.Locale;

@Configuration @Slf4j
public class ApplicationConfig {
    @Bean
    public SimpleObjectMapper sharedObjectMapper() {
        return SimpleObjectMapper.SHARED_MAPPER;
    }

    @Bean
    public AcceptHeaderLocaleResolver localeResolver() {
        AcceptHeaderLocaleResolver localeResolver = new AcceptHeaderLocaleResolver();
        localeResolver.setDefaultLocale(Locale.CHINA); // Default to US English
        return localeResolver;
    }

    @Bean @Profile("dev")
    public ReloadableResourceBundleMessageSource messageSource() {
        String baseDir = System.getProperty("user.dir");
        ReloadableResourceBundleMessageSource messageSource = new ReloadableResourceBundleMessageSource();
        messageSource.setBasename("file:" + baseDir + "/admiz/src/main/resources/i18n/messages");
        messageSource.setDefaultEncoding("UTF-8");
        messageSource.setUseCodeAsDefaultMessage(true);
        messageSource.setCacheMillis(2000);
        return messageSource;
    }

    @Bean @Profile("dev")
    public Loader<?> pebbleLoader() {
        FileLoader fileLoader = new FileLoader() {
            @Override
            public Reader getReader(String templateName) {
                log.info("devPabbleTemplateLoader: getReader={}", templateName);
                return super.getReader(templateName);
            }
        };

        // String baseDir = System.getProperty("user.dir");
        // fileLoader.setPrefix(baseDir + "/admiz/src/main/resources/templates");
        // fileLoader.setSuffix("");

        return fileLoader;
    }

    @Bean @Profile("dev")
    public PebbleViewResolver pebbleViewResolver(PebbleEngine engine) {
        PebbleViewResolver viewResolver = new PebbleViewResolver(engine);
        viewResolver.setPrefix(System.getProperty("user.dir") + "/resources/templates");
        // viewResolver.setSuffix("");
        // (new Syntax.Builder())
        return viewResolver;
    }


}
