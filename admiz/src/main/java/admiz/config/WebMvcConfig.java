package admiz.config;

import admiz.common.setting.ApplicationSetting;
import admiz.extension.StringToBooleanConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.format.FormatterRegistry;
import org.springframework.http.CacheControl;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.i18n.LocaleChangeInterceptor;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Configuration @Slf4j
public class WebMvcConfig implements WebMvcConfigurer {
    @Autowired
    private Environment environment;
    @Autowired
    private ApplicationSetting appSetting;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        LocaleChangeInterceptor localeInterceptor = new LocaleChangeInterceptor();
        localeInterceptor.setParamName("lang");
        registry.addInterceptor(localeInterceptor);
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        List<String> staticDirList = new ArrayList<>();
        if(appSetting.isDevEnvironment()) {
            String userDir = System.getProperty("user.dir");
            String fileSystemStaticDir = "file:" + userDir + "/resources/static/";
            staticDirList.add(fileSystemStaticDir);

            log.info("Running in dev mode, added file system static dir: {}", fileSystemStaticDir);
        }

        staticDirList.add("classpath:/static/");

        String[] staticDirs = staticDirList.toArray(new String[0]);

        registry.addResourceHandler("/static/**")
                .addResourceLocations(staticDirs);
    }

    @Override
    public void addFormatters(FormatterRegistry registry) {
        registry.addConverter(new StringToBooleanConverter());
    }
}
