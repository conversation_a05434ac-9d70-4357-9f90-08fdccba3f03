package admiz.config;

import admiz.auth.shiro.AppModularRealmAuthenticator;
import admiz.auth.shiro.CustomShiroSessionSerializer;
import admiz.auth.shiro.CustomSubjectFactory;
import admiz.auth.shiro.DbSessionDAO;
import admiz.auth.shiro.realm.SystemUserClassicRealm;
import admiz.system.repository.SystemUserRepository;
import admiz.system.service.SystemUserService;
import jakarta.servlet.Filter;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authc.pam.AtLeastOneSuccessfulStrategy;
import org.apache.shiro.cache.*;
import org.apache.shiro.lang.util.SoftHashMap;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.mgt.SubjectFactory;
import org.apache.shiro.realm.Realm;
import org.apache.shiro.session.Session;
import org.apache.shiro.session.mgt.SessionContext;
import org.apache.shiro.session.mgt.SessionFactory;
import org.apache.shiro.session.mgt.SessionManager;
import org.apache.shiro.session.mgt.SimpleSession;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.apache.shiro.spring.web.ShiroUrlPathHelper;
import org.apache.shiro.web.filter.InvalidRequestFilter;
import org.apache.shiro.web.filter.authc.AnonymousFilter;
import org.apache.shiro.web.filter.authc.FormAuthenticationFilter;
import org.apache.shiro.web.filter.authc.LogoutFilter;
import org.apache.shiro.web.filter.authc.UserFilter;
import org.apache.shiro.web.filter.authz.RolesAuthorizationFilter;
import org.apache.shiro.web.mgt.CookieRememberMeManager;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.apache.shiro.web.servlet.SimpleCookie;
import org.apache.shiro.web.session.mgt.DefaultWebSessionManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.context.event.ApplicationContextEvent;
import org.springframework.context.event.EventListener;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.web.context.support.RequestHandledEvent;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.util.Base64;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Configuration
public class ShiroConfig {
    private Map<String, String> createFilterChainDefinitionMap() {
        // Shiro连接约束配置，即过滤链的定义

        Map<String, String> filterChain = new LinkedHashMap<>();
        // 对静态资源设置匿名访问
        filterChain.put("/favicon.ico**", "anon");
        filterChain.put("/static/**", "anon");
        filterChain.put("/public/**", "anon");
        filterChain.put("/dist/**", "anon");
        filterChain.put("/libs/**", "anon");
        filterChain.put("/js/**", "anon");
        filterChain.put("/preview/**", "anon");

        filterChain.put("/login", "anon");
        filterChain.put("/api/login", "anon");
        filterChain.put("/login/**", "anon");
        filterChain.put("/security/captcha", "anon");

        filterChain.put("/logout", "logout");

        //回调
        filterChain.put("/backend/callback/**","anon");
        filterChain.put("/api/public/**","anon");

        // 管理员权限
        filterChain.put("/admin/**", "roles[ADMIN]");

        // 系统管理权限
        filterChain.put("/system/mgmt/**", "user");
        filterChain.put("/api/system/mgmt/**", "user");

        // 用户管理权限
        filterChain.put("/system/mgmt/users/**", "perms[system:user:view]");
        filterChain.put("/api/system/mgmt/users", "perms[system:user:view]");
        filterChain.put("/api/system/mgmt/users/*", "perms[system:user:view]");

        // 角色管理权限
        filterChain.put("/system/mgmt/roles/**", "perms[system:role:view]");
        filterChain.put("/api/system/mgmt/roles", "perms[system:role:view]");
        filterChain.put("/api/system/mgmt/roles/*", "perms[system:role:view]");

        // 资源管理权限
        filterChain.put("/system/mgmt/resources/**", "perms[system:resource:view]");
        filterChain.put("/api/system/mgmt/resources", "perms[system:resource:view]");
        filterChain.put("/api/system/mgmt/resources/*", "perms[system:resource:view]");

        // 所有请求需要认证
        filterChain.put("/**", "user");

        return filterChain;
    }

    private Map<String, Filter> createFilters() {
        Map<String, Filter> filters = new LinkedHashMap<>();

        InvalidRequestFilter invalidRequestFilter = new InvalidRequestFilter();
        invalidRequestFilter.setBlockNonAscii(false);

        filters.put("invalidRequest", invalidRequestFilter);
        filters.put("anon", new AnonymousFilter());
        filters.put("authc", new FormAuthenticationFilter());
        filters.put("logout", new LogoutFilter());
        filters.put("roles", new RolesAuthorizationFilter());
        filters.put("user", new UserFilter());

        return filters;
    }
    /**
     * Shiro过滤器配置
     */
    @Bean
    public ShiroFilterFactoryBean shiroFilterFactoryBean(SecurityManager securityManager, Environment env)
    {
        ShiroFilterFactoryBean shiroFilterFactoryBean = new ShiroFilterFactoryBean();
        // Shiro的核心安全接口,这个属性是必须的
        shiroFilterFactoryBean.setSecurityManager(securityManager);
        // 身份认证失败，则跳转到登录页面的配置
        shiroFilterFactoryBean.setLoginUrl("/login");
        // 权限认证失败，则跳转到指定页面
        shiroFilterFactoryBean.setUnauthorizedUrl("/errors/401-unauthorized");

        String contextPath = env.getProperty("server.servlet.context-path", "/");
        log.info("ShiroConfig: contextPath={}", contextPath);

        shiroFilterFactoryBean.setFilterChainDefinitionMap(createFilterChainDefinitionMap());

        shiroFilterFactoryBean.setFilters(createFilters());

        return shiroFilterFactoryBean;
    }

    @Bean
    @Primary
    public RequestMappingHandlerMapping overridedRequestMappingHandlerMapping() {
        RequestMappingHandlerMapping mapping = new RequestMappingHandlerMapping();
        mapping.setUrlPathHelper(new ShiroUrlPathHelper());
        return mapping;
    }

    @Component @Profile("dev")
    static class ShiroCacheManager extends AbstractCacheManager {
        Cache mapCache = null;
        @Override
        protected <Serializable, Session> Cache<Serializable, Session> createCache(String name) throws CacheException {
            if(mapCache == null) {
                mapCache = new MapCache<>(name, new SoftHashMap<Serializable, Session>());
            }
            return mapCache;
        }

        @EventListener(classes = {ApplicationContextEvent.class})
        public void handleContextStart(ApplicationContextEvent cse) {
            System.out.println("Handling context started event.");
        }


        @EventListener(classes = {RequestHandledEvent.class})
        public void onRequestHandled(RequestHandledEvent event) {
            String sessionId = event.getSessionId();
            if(mapCache != null && sessionId != null) {
                mapCache.remove(sessionId);
            }
            // log.info("onRequestHandled：{}", event.getDescription());
        }
    };

//    @Bean
//    public CacheManager localJvmCacheManager() {
//
//        return new MemoryConstrainedCacheManager();
//    }


    @Bean
    public AppModularRealmAuthenticator appModularRealmAuthenticator(){
        //自己重写的ModularRealmAuthenticator
        AppModularRealmAuthenticator appModularRealmAuthenticator = new AppModularRealmAuthenticator();
        appModularRealmAuthenticator.setAuthenticationStrategy(new AtLeastOneSuccessfulStrategy());//这里为默认策略：如果有一个或多个Realm验证成功，所有的尝试都被认为是成功的，如果没有一个验证成功，则该次尝试失败
        return appModularRealmAuthenticator;
    }

    public SessionFactory sessionFactory() {
        return new SessionFactory() {
            @Override
            public Session createSession(SessionContext initData) {
                if (initData != null) {
                    String host = initData.getHost();
                    if (host != null) {
                        return new SimpleSession(host);
                    }
                }
                return new SimpleSession();
            }
        };
    }

    // Cookie的过期时间，秒为单位, -1表示关闭浏览器就马上过期
    @Value("${shiro.cookie.maxAge: -1}")
    private int maxAge = -1;

    // session 过期时间，单位是毫秒
    @Value("${shiro.session.expireTime: 3600000}")
    private long expireTime;

    @Bean
    public SessionManager sessionManager(DbSessionDAO dbSessionDAO, CacheManager cacheManager) {
        SimpleCookie template = new SimpleCookie("AzSID");
        template.setHttpOnly(true);
        template.setMaxAge(maxAge);

        DefaultWebSessionManager sessionManager = new DefaultWebSessionManager();
        sessionManager.setSessionDAO(dbSessionDAO);
        sessionManager.setSessionIdCookie(template);
        sessionManager.setSessionIdCookieEnabled(true);
        sessionManager.setSessionFactory(sessionFactory());
        sessionManager.setGlobalSessionTimeout(expireTime);
        sessionManager.setCacheManager(cacheManager);
        return sessionManager;
    }

    //cookie cipherKey密钥
    @Value("${shiro.cookie.cipherKey}")
    private String cipherKey;

    @Value("${shiro.rememberMe.enabled: false}")
    private boolean rememberMeEnabled;

    @Bean
    public CookieRememberMeManager rememberMeManager(){
        SimpleCookie template = new SimpleCookie("AzMe");
        template.setHttpOnly(true);
        template.setMaxAge(maxAge);

        CookieRememberMeManager cookieRememberMeManager = new CookieRememberMeManager();
        cookieRememberMeManager.setCookie(template);
        cookieRememberMeManager.setCipherKey(Base64.getDecoder().decode(cipherKey));
        cookieRememberMeManager.setSerializer(new CustomShiroSessionSerializer<>());
        return cookieRememberMeManager;
    }

    @Bean("securityManager")
    public DefaultWebSecurityManager securityManager(AppModularRealmAuthenticator authenticator,
                                                     SystemUserClassicRealm classicRealm,
                                                     SessionManager sessionManager,
                                                     CookieRememberMeManager rememberMeManager,
                                                     SystemUserService userService) {
        DefaultWebSecurityManager securityManager = new DefaultWebSecurityManager();

        List<Realm> realms = List.of(classicRealm);

        authenticator.setRealms(realms);
        securityManager.setRealms(realms);
        securityManager.setAuthenticator(authenticator);
        securityManager.setSessionManager(sessionManager);
        if(rememberMeEnabled){
            securityManager.setRememberMeManager(rememberMeManager);
        }

        CustomSubjectFactory customSubjectFactory = new CustomSubjectFactory(userService);
        securityManager.setSubjectFactory(customSubjectFactory);

        return securityManager;
    }
}
