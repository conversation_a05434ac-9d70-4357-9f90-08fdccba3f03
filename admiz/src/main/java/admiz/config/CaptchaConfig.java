package admiz.config;

import com.google.code.kaptcha.impl.DefaultKaptcha;
import com.google.code.kaptcha.util.Config;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.*;

import java.util.*;
import java.util.stream.StreamSupport;

import static com.google.code.kaptcha.Constants.*;

@Slf4j
@Configuration
public class CaptchaConfig {
    @Bean(name = "textCaptchaProducer")
    public DefaultKaptcha textCaptchaProducer(Environment env) {
        Properties properties = new Properties();
        MutablePropertySources propSrcs = ((AbstractEnvironment) env).getPropertySources();
        StreamSupport.stream(propSrcs.spliterator(), false)
                .filter(ps -> ps instanceof EnumerablePropertySource)
                .map(ps -> ((EnumerablePropertySource<?>) ps).getPropertyNames())
                .flatMap(Arrays::<String>stream)
                .filter(x -> x.startsWith("mfa.kaptcha."))
                .distinct()
                .forEach(propName -> {
                    String propValue = env.getProperty(propName);
                    if(propValue == null) {
                        log.warn("propValue is null: {}", propValue);
                        propValue = "";
                    }

                    propName = propName.substring("mfa.".length());
                    properties.setProperty(propName, propValue);
                });

        Config config = new Config(properties);
        DefaultKaptcha defaultKaptcha = new DefaultKaptcha();
        defaultKaptcha.setConfig(config);
        return defaultKaptcha;
    }
}
