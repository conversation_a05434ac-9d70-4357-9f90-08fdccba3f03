package admiz.common.auth;

import lombok.extern.slf4j.Slf4j;

/**
 * 数据权限工具类
 */
@Slf4j
public class DataScopeUtils {
    
    /**
     * 获取当前数据权限的SQL条件
     * 
     * @return SQL条件字符串，如果没有权限限制则返回null
     */
    public static String getCurrentDataScopeCondition() {
        DataScopeHandler.DataScopeContext context = DataScopeHandler.getCurrentDataScopeContext();
        if (context == null || !context.hasCondition()) {
            return null;
        }
        return context.getSqlCondition();
    }
    
    /**
     * 检查是否有数据权限限制
     * 
     * @return true表示有权限限制，false表示无限制
     */
    public static boolean hasDataScopeRestriction() {
        DataScopeHandler.DataScopeContext context = DataScopeHandler.getCurrentDataScopeContext();
        return context != null && context.hasCondition();
    }
    
    /**
     * 获取当前数据权限类型
     * 
     * @return 数据权限类型，如果没有设置则返回null
     */
    public static DataScope.DataScopeType getCurrentDataScopeType() {
        DataScopeHandler.DataScopeContext context = DataScopeHandler.getCurrentDataScopeContext();
        return context != null ? context.getActualDataScopeType() : null;
    }
    
    /**
     * 检查是否是全部数据权限
     * 
     * @return true表示有全部数据权限，false表示有限制
     */
    public static boolean hasAllDataPermission() {
        DataScope.DataScopeType scopeType = getCurrentDataScopeType();
        return scopeType == null || scopeType == DataScope.DataScopeType.ALL;
    }
    
    /**
     * 为SQL查询添加数据权限条件
     * 
     * @param originalSql 原始SQL
     * @param whereKeyword WHERE关键字（如"WHERE"或"AND"）
     * @return 添加了数据权限条件的SQL
     */
    public static String addDataScopeCondition(String originalSql, String whereKeyword) {
        String condition = getCurrentDataScopeCondition();
        if (condition == null || condition.trim().isEmpty()) {
            return originalSql;
        }
        
        return originalSql + " " + whereKeyword + " " + condition;
    }
    
    /**
     * 为SQL查询添加数据权限条件（自动判断WHERE或AND）
     * 
     * @param originalSql 原始SQL
     * @return 添加了数据权限条件的SQL
     */
    public static String addDataScopeCondition(String originalSql) {
        String condition = getCurrentDataScopeCondition();
        if (condition == null || condition.trim().isEmpty()) {
            return originalSql;
        }
        
        // 简单判断是否已经有WHERE子句
        String upperSql = originalSql.toUpperCase();
        String whereKeyword = upperSql.contains(" WHERE ") ? "AND" : "WHERE";
        
        return addDataScopeCondition(originalSql, whereKeyword);
    }
    
    /**
     * 构建用户数据权限条件
     * 
     * @param userIdColumn 用户ID列名
     * @param userId 用户ID
     * @return SQL条件
     */
    public static String buildUserDataScopeCondition(String userIdColumn, Long userId) {
        return String.format("%s = %d", userIdColumn, userId);
    }
    

    
    /**
     * 记录数据权限应用日志
     * 
     * @param operation 操作描述
     * @param condition 权限条件
     */
    public static void logDataScopeApplication(String operation, String condition) {
        DataScopeHandler.DataScopeContext context = DataScopeHandler.getCurrentDataScopeContext();
        if (context != null) {
            log.debug("应用数据权限 - 操作: {}, 用户: {}, 权限类型: {}, 条件: {}", 
                    operation, context.getUserId(), context.getActualDataScopeType(), condition);
        }
    }
}
