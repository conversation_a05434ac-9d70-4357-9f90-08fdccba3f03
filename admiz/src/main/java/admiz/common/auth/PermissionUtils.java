package admiz.common.auth;

import admiz.system.service.SystemPermissionService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

// 权限验证工具类
@Component
@RequiredArgsConstructor
public class PermissionUtils {
    
    private final SystemPermissionService permissionService;

    // 检查当前用户是否拥有指定权限
    public boolean hasPermission(String permission) {
        return permissionService.currentUserHasPermission(permission);
    }

    // 检查当前用户是否拥有任意一个权限
    public boolean hasAnyPermission(String... permissions) {
        return permissionService.currentUserHasAnyPermission(Arrays.asList(permissions));
    }

    // 检查当前用户是否拥有所有权限
    public boolean hasAllPermissions(String... permissions) {
        return permissionService.currentUserHasAllPermissions(Arrays.asList(permissions));
    }

    // 检查当前用户是否拥有指定角色
    public boolean hasRole(String role) {
        return permissionService.currentUserHasRole(role);
    }

    // 检查当前用户是否拥有任意一个角色
    public boolean hasAnyRole(String... roles) {
        return permissionService.currentUserHasAnyRole(Arrays.asList(roles));
    }

    // 检查当前用户是否拥有所有角色
    public boolean hasAllRoles(String... roles) {
        return permissionService.currentUserHasAllRoles(Arrays.asList(roles));
    }

    // 检查是否已登录
    public boolean isAuthenticated() {
        return permissionService.isAuthenticated();
    }

    // 检查是否是超级管理员
    public boolean isSuperAdmin() {
        return permissionService.currentUserIsSuperAdmin();
    }

    // 获取当前用户ID
    public Long getCurrentUserId() {
        return permissionService.getCurrentUserId();
    }

    // 权限验证断言方法
    public void requirePermission(String permission) {
        if (!hasPermission(permission)) {
            throw new SecurityException("权限不足: " + permission);
        }
    }

    // 角色验证断言方法
    public void requireRole(String role) {
        if (!hasRole(role)) {
            throw new SecurityException("角色不足: " + role);
        }
    }

    // 登录验证断言方法
    public void requireAuthenticated() {
        if (!isAuthenticated()) {
            throw new SecurityException("需要登录");
        }
    }
}
