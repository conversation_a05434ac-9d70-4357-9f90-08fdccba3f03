package admiz.common.auth;

import admiz.common.errorhandling.AppErrors;
import admiz.system.service.SystemPermissionAuditService;
import admiz.system.service.SystemPermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;

// 权限拦截器
@Slf4j
@Aspect
@Component
@Order(1)
@RequiredArgsConstructor
public class PermissionInterceptor {

    private final SystemPermissionService permissionService;
    private final SystemPermissionAuditService auditService;

    // 拦截带有@RequiresPermission注解的方法
    @Around("@annotation(admiz.common.auth.RequiresPermission) || @within(admiz.common.auth.RequiresPermission)")
    public Object checkPermission(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();

        if (!permissionService.isAuthenticated()) {
            throw AppErrors.Auth.AUTHENTICATION_REQUIRED.toException();
        }

        RequiresPermission annotation = getPermissionAnnotation(joinPoint);
        if (annotation != null) {
            String[] permissions = annotation.value();
            RequiresPermission.Logical logical = annotation.logical();

            if (permissions.length > 0) {
                Long userId = permissionService.getCurrentUserId();
                String userLogin = permissionService.getCurrentUserLogin();

                for (String permission : permissions) {
                    boolean hasPermission = permissionService.currentUserHasPermission(permission);
                    long duration = System.currentTimeMillis() - startTime;

                    // 记录权限检查日志
                    auditService.logPermissionCheck(userId, userLogin, permission, hasPermission, duration);

                    if (!hasPermission && logical == RequiresPermission.Logical.AND) {
                        log.warn("权限验证失败 - 用户id: {}, 用户名: {}, 需要权限: {}, 逻辑: {}",
                                userId, userLogin, Arrays.toString(permissions), logical);
                        throw AppErrors.Auth.ACCESS_DENIED.toException("权限不足");
                    } else if (hasPermission && logical == RequiresPermission.Logical.OR) {
                        break; // OR逻辑下，有一个权限即可
                    }
                }

                // 如果是OR逻辑，需要检查是否至少有一个权限
                if (logical == RequiresPermission.Logical.OR) {
                    boolean hasAnyPermission = checkUserPermissions(permissions, logical);
                    if (!hasAnyPermission) {
                        log.warn("权限验证失败 - 用户id: {}, 用户名: {}, 需要权限: {}, 逻辑: {}",
                                userId, userLogin, Arrays.toString(permissions), logical);
                        throw AppErrors.Auth.ACCESS_DENIED.toException("权限不足");
                    }
                }
            }
        }

        return joinPoint.proceed();
    }

    // 拦截带有@RequiresRole注解的方法
    @Around("@annotation(admiz.common.auth.RequiresRole) || @within(admiz.common.auth.RequiresRole)")
    public Object checkRole(ProceedingJoinPoint joinPoint) throws Throwable {
        if (!permissionService.isAuthenticated()) {
            throw AppErrors.Auth.AUTHENTICATION_REQUIRED.toException();
        }

        RequiresRole annotation = getRoleAnnotation(joinPoint);
        if (annotation != null) {
            String[] roles = annotation.value();
            RequiresRole.Logical logical = annotation.logical();
            
            if (roles.length > 0) {
                boolean hasRole = checkUserRoles(roles, logical);
                if (!hasRole) {
                    log.warn("角色验证失败 - 用户id: {}, 用户名: {}, 需要角色: {}, 逻辑: {}",
                            permissionService.getCurrentUserId(), permissionService.getCurrentUserLogin(),
                            Arrays.toString(roles), logical);
                    throw AppErrors.Auth.ACCESS_DENIED.toException("角色不足");
                }
            }
        }

        return joinPoint.proceed();
    }

    private RequiresPermission getPermissionAnnotation(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        
        // 先检查方法级注解
        RequiresPermission annotation = method.getAnnotation(RequiresPermission.class);
        if (annotation != null) {
            return annotation;
        }
        
        // 再检查类级注解
        return method.getDeclaringClass().getAnnotation(RequiresPermission.class);
    }

    private RequiresRole getRoleAnnotation(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        RequiresRole annotation = method.getAnnotation(RequiresRole.class);
        if (annotation != null) {
            return annotation;
        }

        return method.getDeclaringClass().getAnnotation(RequiresRole.class);
    }

    private boolean checkUserPermissions(String[] permissions, RequiresPermission.Logical logical) {
        List<String> permissionList = Arrays.asList(permissions);
        
        if (logical == RequiresPermission.Logical.AND) {
            return permissionService.currentUserHasAllPermissions(permissionList);
        } else {
            return permissionService.currentUserHasAnyPermission(permissionList);
        }
    }

    private boolean checkUserRoles(String[] roles, RequiresRole.Logical logical) {
        List<String> roleList = Arrays.asList(roles);
        
        if (logical == RequiresRole.Logical.AND) {
            return permissionService.currentUserHasAllRoles(roleList);
        } else {
            return permissionService.currentUserHasAnyRole(roleList);
        }
    }
}
