package admiz.common.auth;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

// 角色验证注解
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequiresRole {
    
    // 角色名称列表
    String[] value();
    
    // 逻辑关系：AND表示需要所有角色，OR表示需要任意一个角色
    Logical logical() default Logical.AND;
    
    enum Logical {
        AND, OR
    }
}
