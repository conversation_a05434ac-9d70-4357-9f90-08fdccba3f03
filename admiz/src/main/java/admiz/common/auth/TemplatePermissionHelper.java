package admiz.common.auth;

import admiz.system.service.SystemPermissionService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

// 模板权限验证助手类（用于Pebble模板）
@Component("permission")
@RequiredArgsConstructor
public class TemplatePermissionHelper {
    
    private final SystemPermissionService permissionService;

    // 检查当前用户是否拥有指定权限
    public boolean has(String permission) {
        return permissionService.currentUserHasPermission(permission);
    }

    // 检查当前用户是否拥有任意一个权限
    public boolean hasAny(String... permissions) {
        return permissionService.currentUserHasAnyPermission(Arrays.asList(permissions));
    }

    // 检查当前用户是否拥有所有权限
    public boolean hasAll(String... permissions) {
        return permissionService.currentUserHasAllPermissions(Arrays.asList(permissions));
    }

    // 检查当前用户是否拥有指定角色
    public boolean hasRole(String role) {
        return permissionService.currentUserHasRole(role);
    }

    // 检查当前用户是否拥有任意一个角色
    public boolean hasAnyRole(String... roles) {
        return permissionService.currentUserHasAnyRole(Arrays.asList(roles));
    }

    // 检查当前用户是否拥有所有角色
    public boolean hasAllRoles(String... roles) {
        return permissionService.currentUserHasAllRoles(Arrays.asList(roles));
    }

    // 检查是否已登录
    public boolean isAuthenticated() {
        return permissionService.isAuthenticated();
    }

    // 检查是否是超级管理员
    public boolean isSuperAdmin() {
        return permissionService.currentUserIsSuperAdmin();
    }

    // 获取当前用户ID
    public Long getCurrentUserId() {
        return permissionService.getCurrentUserId();
    }

    // 权限验证的便捷方法
    public boolean check(String expression) {
        try {
            // 支持格式：permission:xxx, role:xxx, authenticated, superAdmin
            if (expression.startsWith("permission:")) {
                String permission = expression.substring("permission:".length());
                return has(permission);
            } else if (expression.startsWith("role:")) {
                String role = expression.substring("role:".length());
                return hasRole(role);
            } else if ("authenticated".equals(expression)) {
                return isAuthenticated();
            } else if ("superAdmin".equals(expression)) {
                return isSuperAdmin();
            }
            
            // 默认当作权限处理
            return has(expression);
        } catch (Exception e) {
            return false;
        }
    }
}
