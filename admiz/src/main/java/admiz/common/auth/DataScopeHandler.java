package admiz.common.auth;

import admiz.system.model.SystemRole;
import admiz.system.service.SystemPermissionService;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * 数据权限处理器
 * 负责处理数据权限的具体逻辑
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DataScopeHandler {
    
    private final SystemPermissionService permissionService;

    private static final ThreadLocal<DataScopeContext> DATA_SCOPE_CONTEXT = new ThreadLocal<>();
    

    public void setDataScopeContext(Long userId, DataScope dataScope) {
        try {
            DataScopeContext context = buildDataScopeContext(userId, dataScope);
            DATA_SCOPE_CONTEXT.set(context);
            log.debug("设置数据权限上下文: userId={}, scopeType={}", userId, dataScope.value());
        } catch (Exception e) {
            log.error("设置数据权限上下文失败: userId={}", userId, e);
        }
    }

    public void clearDataScopeContext() {
        DATA_SCOPE_CONTEXT.remove();
    }

    public static DataScopeContext getCurrentDataScopeContext() {
        return DATA_SCOPE_CONTEXT.get();
    }

    private DataScopeContext buildDataScopeContext(Long userId, DataScope dataScope) {
        DataScopeContext context = new DataScopeContext();
        context.setUserId(userId);
        context.setDataScopeType(dataScope.value());
        context.setUserAlias(dataScope.userAlias());
        context.setDeptAlias(dataScope.deptAlias());
        context.setUserIdColumn(dataScope.userIdColumn());
        context.setDeptIdColumn(dataScope.deptIdColumn());

        // 根据用户角色确定实际的数据权限范围
        DataScope.DataScopeType actualScopeType = determineActualDataScope(userId, dataScope.value());
        context.setActualDataScopeType(actualScopeType);

        // 根据数据权限类型设置具体的权限条件
        String sqlCondition = buildSqlCondition(userId, actualScopeType, dataScope);
        context.setSqlCondition(sqlCondition);

        return context;
    }

    // 构建SQL权限条件
    private String buildSqlCondition(Long userId, DataScope.DataScopeType scopeType, DataScope dataScope) {
        switch (scopeType) {
            case ALL:
                return null; // 全部数据权限，不需要条件

            case SELF:
                return String.format("%s.%s = %d",
                    dataScope.userAlias(), dataScope.userIdColumn(), userId);

            case DEPARTMENT:
                Long userDeptId = getUserDeptId(userId);
                if (userDeptId == null) {
                    return String.format("%s.%s = %d",
                        dataScope.userAlias(), dataScope.userIdColumn(), userId);
                }
                return String.format("%s.%s = %d",
                    dataScope.deptAlias(), dataScope.deptIdColumn(), userDeptId);

            case DEPT_AND_CHILD:
                Set<Long> deptIds = getUserDeptAndChildIds(userId);
                if (deptIds.isEmpty()) {
                    return String.format("%s.%s = %d",
                        dataScope.userAlias(), dataScope.userIdColumn(), userId);
                }
                return String.format("%s.%s IN (%s)",
                    dataScope.deptAlias(), dataScope.deptIdColumn(),
                    deptIds.stream().map(String::valueOf).reduce((a, b) -> a + "," + b).orElse("0"));

            case CUSTOM:
                return getCustomDataScopeCondition(userId);

            default:
                return String.format("%s.%s = %d",
                    dataScope.userAlias(), dataScope.userIdColumn(), userId);
        }
    }

    private DataScope.DataScopeType determineActualDataScope(Long userId, DataScope.DataScopeType requestedScope) {
        try {
            if (permissionService.currentUserIsSuperAdmin()) {
                return DataScope.DataScopeType.ALL;
            }

            List<SystemRole> userRoles = permissionService.getCurrentUserRoles();
            
            // 如果用户有管理员角色，给予全部数据权限
            boolean hasAdminRole = userRoles.stream()
                    .anyMatch(role -> "ROLE_ADMIN".equals(role.name()) || "ADMIN".equals(role.name()));
            
            if (hasAdminRole) {
                return DataScope.DataScopeType.ALL;
            }
            
            // 否则使用请求的权限范围
            return requestedScope;
            
        } catch (Exception e) {
            log.error("确定数据权限范围失败: userId={}", userId, e);
            return DataScope.DataScopeType.SELF;
        }
    }

    private Long getUserDeptId(Long userId) {
        try {
            // TODO: 实现获取用户部门ID的逻辑
            log.debug("获取用户部门ID: userId={}", userId);
            return null;
        } catch (Exception e) {
            log.error("获取用户部门ID失败: userId={}", userId, e);
            return null;
        }
    }


    private Set<Long> getUserDeptAndChildIds(Long userId) {
        try {
            // TODO: 实现获取用户部门及下级部门ID的逻辑
            log.debug("获取用户部门及下级部门ID: userId={}", userId);
            return Set.of();
        } catch (Exception e) {
            log.error("获取用户部门及下级部门ID失败: userId={}", userId, e);
            return Set.of();
        }
    }

    private String getCustomDataScopeCondition(Long userId) {
        try {
            // TODO: 实现自定义数据权限条件的逻辑
            log.debug("获取自定义数据权限条件: userId={}", userId);
            return null;
        } catch (Exception e) {
            log.error("获取自定义数据权限条件失败: userId={}", userId, e);
            return null;
        }
    }

    @Setter
    @Getter
    public static class DataScopeContext {
        private Long userId;
        private DataScope.DataScopeType dataScopeType;
        private DataScope.DataScopeType actualDataScopeType;
        private String userAlias;
        private String deptAlias;
        private String userIdColumn;
        private String deptIdColumn;
        private String sqlCondition;

        public boolean hasCondition() {
            return sqlCondition != null && !sqlCondition.trim().isEmpty();
        }

        // 是否需要部门权限过滤
        public boolean needsDeptFilter() {
            return actualDataScopeType == DataScope.DataScopeType.DEPARTMENT
                || actualDataScopeType == DataScope.DataScopeType.DEPT_AND_CHILD;
        }


        // 是否是自定义权限
        public boolean isCustomScope() {
            return actualDataScopeType == DataScope.DataScopeType.CUSTOM;
        }
    }
}
