package admiz.common.auth;

import lombok.Getter;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 数据权限注解
 * 用于控制数据访问范围
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface DataScope {
    
    //数据权限类型
    DataScopeType value() default DataScopeType.ALL;

    //用户表别名
    String userAlias() default "u";

    //部门表别名（用于部门权限）
    String deptAlias() default "d";

    //用户ID字段名
    String userIdColumn() default "user_id";

    //部门ID字段名
    String deptIdColumn() default "dept_id";

    //是否启用数据权限过滤
    boolean enabled() default true;

    @Getter
    enum DataScopeType {
        ALL("全部数据权限"),
        SELF("仅本人数据权限"),
        DEPARTMENT("本部门数据权限"),
        DEPT_AND_CHILD("本部门及下级部门数据权限"),
        CUSTOM("自定义数据权限");

        private final String description;

        DataScopeType(String description) {
            this.description = description;
        }

    }
}
