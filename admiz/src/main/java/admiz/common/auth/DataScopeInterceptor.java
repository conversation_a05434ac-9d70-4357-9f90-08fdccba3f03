package admiz.common.auth;

import admiz.system.service.SystemPermissionAuditService;
import admiz.system.service.SystemPermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 数据权限拦截器
 * 用于拦截带有@DataScope注解的方法，实现数据权限控制
 */
@Slf4j
@Aspect
@Component
@Order(2) // 在权限拦截器之后执行
@RequiredArgsConstructor
public class DataScopeInterceptor {

    private final SystemPermissionService permissionService;
    private final DataScopeHandler dataScopeHandler;
    private final SystemPermissionAuditService auditService;
    
    /**
     * 拦截带有@DataScope注解的方法
     */
    @Around("@annotation(admiz.common.auth.DataScope) || @within(admiz.common.auth.DataScope)")
    public Object handleDataScope(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();

        // 检查用户是否已登录
        if (!permissionService.isAuthenticated()) {
            return joinPoint.proceed();
        }

        DataScope dataScope = getDataScopeAnnotation(joinPoint);
        if (dataScope == null || !dataScope.enabled()) {
            return joinPoint.proceed();
        }

        Long currentUserId = permissionService.getCurrentUserId();
        String currentUserLogin = permissionService.getCurrentUserLogin();
        if (currentUserId == null) {
            return joinPoint.proceed();
        }

        try {
            // 设置数据权限上下文
            dataScopeHandler.setDataScopeContext(currentUserId, dataScope);

            log.debug("应用数据权限过滤 - 用户id: {}, 用户名: {}, 权限类型: {}",
                    currentUserId, currentUserLogin, dataScope.value());

            Object result = joinPoint.proceed();

            // 记录数据权限应用日志
            long duration = System.currentTimeMillis() - startTime;
            String sqlCondition = DataScopeUtils.getCurrentDataScopeCondition();
            auditService.logDataScopeApplied(currentUserId, currentUserLogin,
                    dataScope.value().name(), sqlCondition, duration);

            return result;
        } finally {
            // 清理数据权限上下文
            dataScopeHandler.clearDataScopeContext();
        }
    }

    private DataScope getDataScopeAnnotation(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        
        // 先检查方法级注解
        DataScope annotation = method.getAnnotation(DataScope.class);
        if (annotation != null) {
            return annotation;
        }
        
        // 再检查类级注解
        return method.getDeclaringClass().getAnnotation(DataScope.class);
    }
}
