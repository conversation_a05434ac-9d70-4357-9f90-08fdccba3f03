package admiz.errorhandling;

import admiz.auth.shiro.AuthErrors;
import admiz.common.errorhandling.*;
import admiz.common.result.Result;
import admiz.common.setting.ApplicationSetting;
import io.pebbletemplates.pebble.PebbleEngine;
import io.pebbletemplates.pebble.template.PebbleTemplate;
import jakarta.annotation.Nonnull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.shiro.authz.UnauthenticatedException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import java.io.IOException;
import java.io.StringWriter;
import java.io.Writer;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@ControllerAdvice
public class CommonExceptionHandler {
    @Autowired
    private PebbleEngine pebbleEngine;
    @Autowired
    private ApplicationSetting applicationSetting;

    @Value("${spring.application.name}")
    protected String applicationName;

    @ExceptionHandler(UnauthenticatedException.class)
    public @ResponseBody ResponseEntity<?> handleUnauthenticatedException(UnauthenticatedException ex, @Nonnull
    NativeWebRequest request) {
//        log.debug("Unhandled exception, application={}, handlerMethod=handleMethodArgumentNotValid", applicationName, ex);
        Result<?> authenticationRequiredResult = AuthErrors.AUTHENTICATION_REQUIRED.toResult();
        ResponseEntity<?> response = new ResponseEntity<>(authenticationRequiredResult, HttpStatus.UNAUTHORIZED);
        return response;
    }

    @ExceptionHandler(AuthErrorDetailException.class)
    public @ResponseBody Result<?> handleAuthErrorDetailException(AuthErrorDetailException authErrorDetailException, NativeWebRequest request) {
        ErrorDetail errorDetail = authErrorDetailException.getErrorDetail();
        ErrorTag tag = authErrorDetailException.getTag();
        return errorDetailsToResult(errorDetail, tag);
    }

    @ExceptionHandler(ErrorDetailException.class)
    public @ResponseBody Result<?> handleErrorDetailException(ErrorDetailException errorDetailException, NativeWebRequest request) {
        return errorDetailsToResult(errorDetailException);
    }

    private Result<?> errorDetailsToResult(ErrorDetail errorDetail, ErrorTag tag) {
        if(tag != null && (errorDetail == AppErrors.CommonError.INVALID_PARAMETER || errorDetail == AppErrors.Auth.INCORRECT_CAPTCHA)) {
            return Result.error(errorDetail, Map.of(tag.getTagName(), errorDetail.getMessage()));
        } else {
            return errorDetail.toResult();
        }
    }

    private Result<?> errorDetailsToResult(ErrorDetailException errorDetailException) {
        ErrorDetail errorDetail = errorDetailException.getErrorDetail();
        ErrorTag tag = errorDetailException.getTag();

        if (tag != null && (errorDetail == AppErrors.CommonError.INVALID_PARAMETER || errorDetail == AppErrors.Auth.INCORRECT_CAPTCHA)) {
            return Result.error(errorDetail, Map.of(tag.getTagName(), errorDetailException.getMessage()));
        } else {
            return new Result<>(errorDetail.getCode(), errorDetailException.getMessage());
        }
    }

    private boolean isAjax(NativeWebRequest request) {
        String header = request.getHeader("X-Requested-With");
        if("XMLHttpRequest".equals(header)) {
            return true;
        }

        String accept = request.getHeader("Accept");
        return accept != null && accept.contains("application/json");
    }

    public ResponseEntity<?> renderErrorPage(Exception ex, NativeWebRequest request) {
        PebbleTemplate compiledTemplate;
        Map<String, Object> context = new HashMap<>();
        context.put("request", request);
        if(!this.applicationSetting.isDevEnvironment()) {
            compiledTemplate = pebbleEngine.getTemplate("/error/error.500.peb.html");
        } else {
            compiledTemplate = pebbleEngine.getTemplate("/dev/exception.peb.html");
            context.put("exception", ex);
            context.put("stackTrace", ExceptionUtils.getStackTrace(ex));
        }

        try {
            Writer writer = new StringWriter();
            compiledTemplate.evaluate(writer, context);
            String output = writer.toString();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(output);
        } catch (IOException e) {
            log.error("Failed to render debug page: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("message", e.getMessage()));
        }
    }

    @ExceptionHandler(Exception.class)
    public @ResponseBody ResponseEntity<?> handleGenericException(Exception ex, NativeWebRequest request) {
        if(!isAjax(request)) {
            return renderErrorPage(ex, request);
        }

        log.error("Unhandled exception, application={}, handleGenericException: ", applicationName, ex);
        Result<?> result = CommonErrors.SERVER_ERROR.toResult();

        return ResponseEntity.ok().body(result);
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public @ResponseBody Result<?> handleHttpRequestMethodNotSupportedException(Exception ex, NativeWebRequest request) {
        return CommonErrors.METHOD_NOT_SUPPORTED.toResult();
    }


    public ResponseEntity<?> render404NotFoundPage(Exception ex, NativeWebRequest request) {
        PebbleTemplate compiledTemplate;
        Map<String, Object> context = new HashMap<>();
        context.put("request", request);
        compiledTemplate = pebbleEngine.getTemplate("/error/error.404.peb.html");

        try {
            Writer writer = new StringWriter();
            compiledTemplate.evaluate(writer, context);
            String output = writer.toString();
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(output);
        } catch (IOException e) {
            log.error("Failed to render debug page: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(Map.of("message", e.getMessage()));
        }
    }

    @ExceptionHandler(NoResourceFoundException.class)
    public @ResponseBody ResponseEntity<?> handleNoResourceFoundException(Exception ex, NativeWebRequest request) {
        if(!isAjax(request)) {
            return render404NotFoundPage(ex, request);
        }
        return new ResponseEntity<>("404 Not Found", HttpStatus.NOT_FOUND);
    }

//    @ExceptionHandler(ConcurrencyFailureException.class)
//    public @ResponseBody Result<?> handleConcurrencyFailure(ConcurrencyFailureException ex, NativeWebRequest request) {
//        log.error("handleBadRequestAlertException:", ex);
//        return R.fail(GeneralError.CONCURRENCY_FAILURE);
//    }

}
