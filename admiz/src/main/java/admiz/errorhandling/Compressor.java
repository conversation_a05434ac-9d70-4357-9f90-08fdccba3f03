package admiz.errorhandling;

import lombok.SneakyThrows;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.zip.Deflater;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;
import java.util.zip.Inflater;

public class Compressor {
    // 压缩 byte[] 数组
    @SneakyThrows
    public static byte[] deflaterCompress(byte[] input, int compressionLevel, boolean gzipFormat)  {
        Deflater compressor = new Deflater(compressionLevel, gzipFormat);
        compressor.setInput(input);
        compressor.finish();
        ByteArrayOutputStream bao = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        while (!compressor.finished()) {
            int count = compressor.deflate(buffer);
            if (count > 0) {
                bao.write(buffer, 0, count);
            }
        }
        compressor.end();
        return bao.toByteArray();
    }

    // 解压缩 byte[] 数组
    @SneakyThrows
    public static byte[] deflaterDecompress(byte[] input, boolean gzipFormat)  {
        Inflater decompressor = new Inflater(gzipFormat);
        decompressor.setInput(input);
        ByteArrayOutputStream bao = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        while (!decompressor.finished()) {
            int count = decompressor.inflate(buffer);
            if (count > 0) {
                bao.write(buffer, 0, count);
            }
        }
        decompressor.end();
        return bao.toByteArray();
    }


    // 使用 GZIP 压缩 byte[] 数组
    @SneakyThrows
    public static byte[] gzipCompress(byte[] bytes)  {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try (GZIPOutputStream gzip = new GZIPOutputStream(baos)) {
            gzip.write(bytes);
        }
        return baos.toByteArray();
    }

    // 使用 GZIP 解压缩 byte[] 数组
    @SneakyThrows
    public static byte[] gzipDecompress(byte[] bytes)  {
        ByteArrayInputStream bais = new ByteArrayInputStream(bytes);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try (GZIPInputStream gzip = new GZIPInputStream(bais)) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = gzip.read(buffer)) > 0) {
                baos.write(buffer, 0, len);
            }
        }
        return baos.toByteArray();
    }
}
