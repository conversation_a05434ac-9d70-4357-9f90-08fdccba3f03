package admiz.auth;

import admiz.auth.shiro.CustomShiroSubject;
import admiz.system.model.SystemUser;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;

public class AuthContext {
//    public static final static AuthContext INSTANCE = new AuthContext();
//    private static ApplicationContext applicationContext;
//
//    public static static void initialize(ApplicationContext applicationContext) {
//        AuthContext.applicationContext = applicationContext;
//    }

    public static boolean isAuthenticated() {
        return SecurityUtils.getSubject().isAuthenticated();
    }

    public static boolean isRememberMe() {
        return SecurityUtils.getSubject().isRemembered();
    }

    public static boolean isRunAs() {
        return SecurityUtils.getSubject().isRunAs();
    }

    public static boolean hasRole(String role) {
        return SecurityUtils.getSubject().hasRole(role);
    }

    public static boolean isPermitted(String permission) {
        return SecurityUtils.getSubject().isPermitted(permission);
    }

    public static SystemUser currentUser() {
        CustomShiroSubject subject = (CustomShiroSubject) SecurityUtils.getSubject();
        return subject.getCurrentUser();
    }

    public static Session session() {
        Session session = SecurityUtils.getSubject().getSession();
        return session;
    }
    public static String sessionId() {
        return SecurityUtils.getSubject().getSession().getId().toString();
    }

    public void getUser() {

    }

//    public static UserInfoVO getUserInfo() {
//        if(!isAuthenticated() && !isRememberMe()) {
//            throw AuthErrors.AUTHENTICATION_REQUIRED.toException();
//        }
//
//        SystemUser systemUser = currentUser();
//
//        List<String> roleNameList = applicationContext.getBean(SystemRoleRepository.class).findUserRoles(systemUser.id()).stream().map(SystemRole::name).toList();
//        SystemResourceFetcher fetcher = Fetchers.SYSTEM_RESOURCE_FETCHER
//                .name()
//                .displayName()
//                .parentId()
//                .icon()
//                .resourceType()
//                .sortNum()
//                .target()
//                .url();
//
//        List<SystemResource> resourceList = applicationContext.getBean(SystemResourceRepository.class).findUserResources(systemUser.id(), fetcher);
//
//        UserInfoVO userInfoVO = new UserInfoVO(systemUser, roleNameList, resourceList);
//
//        return userInfoVO;
//    }
}
