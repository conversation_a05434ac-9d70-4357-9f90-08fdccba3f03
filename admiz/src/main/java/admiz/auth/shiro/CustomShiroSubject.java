package admiz.auth.shiro;

import admiz.system.model.SystemUser;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.web.subject.support.WebDelegatingSubject;

public class CustomShiroSubject extends WebDelegatingSubject {
    public static String CURRENT_USER_ATTRIBUTE_KEY = CustomShiroSubject.class.getName() + "_CURRENT_USER_ATTRIBUTE_KEY";

    void setCurrentUser(SystemUser currentUser) {
        this.getServletRequest().setAttribute(CURRENT_USER_ATTRIBUTE_KEY, currentUser);
    }

    public SystemUser getCurrentUser() {
        SystemUser user = (SystemUser) this.getServletRequest().getAttribute(CURRENT_USER_ATTRIBUTE_KEY);
        return user;
    }

    public CustomShiroSubject(PrincipalCollection principals, boolean authenticated,
                              String host, Session session,
                              ServletRequest request, ServletResponse response,
                              SecurityManager securityManager) {
        this(principals, authenticated, host, session, true, request, response, securityManager);
    }

    //since 1.2
    public CustomShiroSubject(PrincipalCollection principals, boolean authenticated,
                                String host, Session session, boolean sessionEnabled,
                                ServletRequest request, ServletResponse response,
                                SecurityManager securityManager) {
        super(principals, authenticated, host, session, sessionEnabled, request, response, securityManager);
    }

}
