package admiz.auth.shiro.token;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.shiro.authc.UsernamePasswordToken;

@Data @EqualsAndHashCode(callSuper = true)
public class UsernamePasswordCaptchaToken extends UsernamePasswordToken {
    private String captcha;

    public UsernamePasswordCaptchaToken(String username, String password, String captcha) {
        this(username, password, captcha, false);
    }
    public UsernamePasswordCaptchaToken(String username, String password, String captcha, boolean rememberMe) {
        super(username, password, rememberMe);
        this.captcha = captcha;
    }

    public String getPasswordAsString() {
        return new String(getPassword());
    }
}
