package admiz.auth.shiro.realm;

import admiz.common.setting.ApplicationSetting;
import admiz.system.repository.SystemUserAuthnRepository;
import admiz.system.repository.SystemUserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j @Service @RequiredArgsConstructor
public class FailureLoginAttemptHandler {
    final SystemUserRepository userRepository;
    final SystemUserAuthnRepository authnRepository;
    final ApplicationSetting applicationSetting;

    protected void handleFailureLoginAttempts(String username) {

    }
}
