package admiz.auth.shiro.realm;

import admiz.common.auth.SecurityMixin;
import admiz.common.errorhandling.AppErrors;
import admiz.system.model.*;
import admiz.system.repository.*;
import admiz.system.service.SystemCaptchaService;
import com.google.common.base.Strings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j @Service @RequiredArgsConstructor
public class SystemUserClassicRealm extends AbstractRealm implements SecurityMixin {
    final static SystemUserAuthn.AuthnType AUTHN_TYPE = SystemUserAuthn.AuthnType.PASSWORD;

    final SystemRoleRepository roleRepository;
    final SystemUserRepository userRepository;
    final SystemUserAuthnRepository authnRepository;
    final SystemResourceRepository resourceRepository;
    final SystemKvStoreRepository kvStoreRepository;
    final SystemCaptchaService captchaService;
    final FailureLoginAttemptHandler failureLoginAttemptHandler;

    @Override
    public boolean supports(AuthenticationToken token) {
        return token instanceof UsernamePasswordToken;
    }

    @Override
    protected List<SystemResource> doGetUserResources(SystemUser user) {
        return resourceRepository.findUserResources(user.id());
    }

    @Override
    protected List<SystemRole> doGetUserRoles(SystemUser user) {
        return roleRepository.findUserRoles(user.id());
    }

    @Override
    protected SystemUser doGetUser(long userId) {
        return userRepository.findById(userId)
                .orElseThrow(AppErrors.Auth.ACCOUNT_NOT_FOUND::toException);
    }

    @Override
    protected boolean autoUnlockUser(SystemUser systemUser) {
        return userRepository.unlock(systemUser);
    }

    @Override
    protected SystemUser doGetUserFromAuthenticationToken(AuthenticationToken authenticationToken) {
        UsernamePasswordToken token = (UsernamePasswordToken) authenticationToken;

        String username = token.getUsername();
        String password = new String(token.getPassword());

        if (Strings.isNullOrEmpty(username)) {
            throw AppErrors.Auth.INCORRECT_CREDENTIALS.toException();
        }
        if (Strings.isNullOrEmpty(password)) {
            throw AppErrors.Auth.INCORRECT_CREDENTIALS.toException();
        }

        SystemUserAuthn userAuthn = authnRepository.findByAuthnIdAndAuthnType(username, AUTHN_TYPE)
                .orElseThrow(() -> AppErrors.Auth.ACCOUNT_NOT_FOUND.toException(String.format("没有找到对应的认证信息, username=%s, type=%s", username, AUTHN_TYPE)));

        if(userAuthn.disabled()) {
            failureLoginAttemptHandler.handleFailureLoginAttempts(username);
            throw AppErrors.Auth.ACCOUNT_LOCKED.toException(String.format("对应的认证信息已经禁用, username=%s, type=%s", username, AUTHN_TYPE));
        }

        if(!getPasswordService().passwordsMatch(password, userAuthn.authnData())) {
            failureLoginAttemptHandler.handleFailureLoginAttempts(username);
            throw AppErrors.Auth.INCORRECT_CREDENTIALS.toException(String.format("密码错误, username=%s, type=%s", username, AUTHN_TYPE));
        }

        // 登录过程中只返回
        SystemUserFetcher loginFetcher = Fetchers.SYSTEM_USER_FETCHER
                .login()
                .realm()
                .avatar()
                .displayName()
                .mobile()
                .email()
                .locked()
                .lockedUntil()
                .sex();

        return userRepository.findByLogin(username, loginFetcher)
                .orElseThrow(() -> AppErrors.Auth.ACCOUNT_NOT_FOUND.toException(String.format("没有找到对应用户, username=%s", token.getUsername())));
    }
}
