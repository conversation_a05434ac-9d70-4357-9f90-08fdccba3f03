package admiz.auth.shiro.realm;

import admiz.auth.shiro.CustomShiroSubject;
import admiz.common.auth.PrincipalTag;
import admiz.common.errorhandling.AppErrors;
import admiz.system.model.SystemResource;
import admiz.system.model.SystemRole;
import admiz.system.model.SystemUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.cache.Cache;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.apache.shiro.subject.Subject;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public abstract class AbstractRealm extends AuthorizingRealm {
    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
        PrincipalTag tag =  (PrincipalTag)principals.getPrimaryPrincipal();

        Subject subject1 = SecurityUtils.getSubject();
        CustomShiroSubject subject = (CustomShiroSubject) subject1;

        SystemUser user = subject.getCurrentUser();
        List<SystemRole> userRoles = doGetUserRoles(user);
        List<SystemResource> resourceList = doGetUserResources(user);

        Set<String> roles = userRoles.stream().map(SystemRole::name).collect(Collectors.toSet());
        Set<String> menus = resourceList.stream().map(SystemResource::permission).collect(Collectors.toSet());

        SimpleAuthorizationInfo info = new SimpleAuthorizationInfo();
        info.setRoles(roles);
        info.setStringPermissions(menus);

        // 管理员拥有所有权限
        if (user.isBuiltinAdmin()) {
            info.addRole("admin");
            info.addStringPermission("*:*:*");
        }

        return info;
    }

    protected abstract List<SystemResource> doGetUserResources(SystemUser user);
    protected abstract List<SystemRole> doGetUserRoles(SystemUser user);
    protected abstract SystemUser doGetUser(long userId);

    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken token) throws AuthenticationException {
        SystemUser systemUser = doGetUserFromAuthenticationToken(token);

//        if(systemUser.disabled()) {
//            throw AppErrors.Auth.ACCOUNT_LOCKED.toException(String.format("账号已经禁用, username=%s", systemUser.login()));
//        }

        if(systemUser.locked()) {
            LocalDateTime lockedUntil = systemUser.lockedUntil();
            if (lockedUntil == null || !lockedUntil.isBefore(LocalDateTime.now())) {
                String msg = String.format("用户已经锁定, 解禁时间：%s", lockedUntil);
                throw AppErrors.Auth.ACCOUNT_LOCKED.toException(msg);
            }

            boolean unlocked = autoUnlockUser(systemUser);// 封禁时间已过，自动解禁
            if(!unlocked) {
                log.info("用户封禁时间已过，自动解禁失败，login={}, lockedUntil={}", systemUser.locked(), lockedUntil);
                throw AppErrors.Auth.ACCOUNT_LOCKED.toException("用户已经锁定");
            }
        }

        return new SimpleAuthenticationInfo(PrincipalTag.of(systemUser.id(), this.getName()), token.getCredentials(), this.getName());
    }

    protected abstract boolean autoUnlockUser(SystemUser systemUser);


    protected abstract SystemUser doGetUserFromAuthenticationToken(AuthenticationToken token);

    /**
     * 清理指定用户授权信息缓存
     */
    public void clearCachedAuthorizationInfo(Object principal) {
        SimplePrincipalCollection principals = new SimplePrincipalCollection(principal, getName());
        this.clearCachedAuthorizationInfo(principals);
    }

    /**
     * 清理所有用户授权信息缓存
     */
    public void clearAllCachedAuthorizationInfo() {
        Cache<Object, AuthorizationInfo> cache = getAuthorizationCache();
        if (cache != null) {
            for (Object key : cache.keys()) {
                cache.remove(key);
            }
        }
    }
}
