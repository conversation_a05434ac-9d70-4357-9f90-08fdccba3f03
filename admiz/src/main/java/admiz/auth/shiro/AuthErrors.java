package admiz.auth.shiro;

import admiz.common.errorhandling.AuthErrorDetail;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AuthErrors implements AuthErrorDetail {
    AUTHENTICATION_REQUIRED(200401, "用户未登录"),
    ACCOUNT_NOT_FOUND(200404, "帐号不存在"),
    ACCOUNT_LOCKED(200410, "帐号已锁定"),
    INVALID_PASSWORD(200420, "帐号或密码错误"),
    INVALID_CAPTCHA(200430, "验证码错误"),
    ;

    final int code;
    final String message;
}
