package admiz.auth.shiro;

import admiz.common.entity.IdGenerator;
import admiz.common.entity.UlIdGenerator;
import admiz.system.model.Immutables;
import admiz.system.model.SystemSession;
import admiz.system.repository.SystemSessionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.session.Session;
import org.apache.shiro.session.UnknownSessionException;
import org.apache.shiro.session.mgt.SimpleSession;
import org.apache.shiro.session.mgt.eis.CachingSessionDAO;
import org.apache.shiro.subject.support.DefaultSubjectContext;
import org.babyfish.jimmer.sql.ast.mutation.SaveMode;
import org.springframework.stereotype.Service;

import java.io.*;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Base64;
import java.util.Collection;
import java.util.List;

@Service @Slf4j @RequiredArgsConstructor
public class DbSessionDAO extends CachingSessionDAO {
    final static CustomShiroSessionSerializer<Session> CUSTOM_SHIRO_SESSION_SERIALIZER = new CustomShiroSessionSerializer<>();

    final SystemSessionRepository systemSessionRepository;

//    final SimpleObjectMapper mapper = createSessionMapper();
//    static SimpleObjectMapper createSessionMapper() {
//        SimpleObjectMapper mapper = new SimpleObjectMapper();
//        mapper.configure(DeserializationFeature.FAIL_ON_IGNORED_PROPERTIES, false);
//        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
//        mapper.registerModule(new ImmutableModule());
//
//        return mapper;
//    }

    @Override
    protected Serializable doCreate(Session session) {
        Serializable sessionId = IdGenerator.nextUlid();
        ((SimpleSession)session).setId(sessionId);
        createAndStoreSession(sessionId, session);

        return sessionId;
    }

    protected void createAndStoreSession(Serializable id, Session session) {
        if (id == null) {
            throw new NullPointerException("id argument cannot be null.");
        }

        byte[] serializedSessionData = CUSTOM_SHIRO_SESSION_SERIALIZER.serialize(session);

        String sessionStr = Base64.getEncoder().encodeToString(serializedSessionData);
        SystemSession systemSession = Immutables.createSystemSession(draft -> {
            draft.setId(id.toString());
            draft.setData(sessionStr);
            LocalDateTime lastAccessTime = session.getLastAccessTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            draft.setLastAccessTime(lastAccessTime);
            draft.setExpireTime(lastAccessTime.plus(Duration.ofMillis(session.getTimeout())));
        });

        systemSessionRepository.save(systemSession, SaveMode.INSERT_ONLY);

        log.debug("DbSessionDAO.createAndStoreSession: sessionId={}", session.getId());
    }

    public Session readSession(Serializable sessionId) throws UnknownSessionException {
        Session session = getCachedSession(sessionId);
        if (session == null) {
            session = doReadSession(sessionId);
        }

        cache(session, sessionId);

        return session;
    }

    @Override
    protected Session doReadSession(Serializable sessionId) {
        if (sessionId == null) {
            throw new NullPointerException("id argument cannot be null.");
        }

        String sessionIdStr = sessionId.toString();
        SystemSession systemSession = systemSessionRepository.findById(sessionIdStr)
                .orElseThrow(() -> new UnknownSessionException(sessionIdStr));
        String data = systemSession.data();

        byte[] decodedSessionData = Base64.getDecoder().decode(data);
        SimpleSession session = (SimpleSession) CUSTOM_SHIRO_SESSION_SERIALIZER.deserialize(decodedSessionData);
        Object attribute = session.getAttribute(DefaultSubjectContext.AUTHENTICATED_SESSION_KEY);
        if(attribute == null) {
            log.debug("DbSessionDAO.doUpdate: sessionId={}, attribute == null", session.getId());
        }

        return session;
    }

    @Override
    protected void doUpdate(Session session) {
        Object attribute = session.getAttribute(DefaultSubjectContext.AUTHENTICATED_SESSION_KEY);
        if(attribute == null) {
            log.debug("DbSessionDAO.doUpdate: sessionId={}, attribute == null", session.getId());
        }
        byte[] serializedSessionData = CUSTOM_SHIRO_SESSION_SERIALIZER.serialize(session);
        String sessionStr = Base64.getEncoder().encodeToString(serializedSessionData);
        SystemSession systemSession = Immutables.createSystemSession(draft -> {
            draft.setId(session.getId().toString());
            draft.setData(sessionStr);
            LocalDateTime lastAccessTime = session.getLastAccessTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            draft.setLastAccessTime(lastAccessTime);
            draft.setExpireTime(lastAccessTime.plus(Duration.ofMillis(session.getTimeout())));
        });

        log.debug("DbSessionDAO.update: sessionId={}, data={}", session.getId(), systemSession.data());

        systemSessionRepository.save(systemSession, SaveMode.UPDATE_ONLY);
    }

    @Override
    protected void doDelete(Session session) {
        systemSessionRepository.deleteById(session.getId().toString());
        log.debug("DbSessionDAO.delete: sessionId={}", session.getId());
    }

    @Override
    public Collection<Session> getActiveSessions() {
        return List.of();
    }
}
