package admiz.auth.shiro;

import admiz.errorhandling.Compressor;
import org.apache.shiro.lang.io.DefaultSerializer;
import org.apache.shiro.lang.io.SerializationException;

import java.util.zip.Deflater;

public class CustomShiroSessionSerializer<T> extends DefaultSerializer<T> {
    public byte[] serialize(T o) throws SerializationException {
        byte[] serialized = super.serialize(o);
        byte[] compressedData = Compressor.deflaterCompress(serialized, Deflater.DEFLATED, false);

        return compressedData;
    }

    public T deserialize(byte[] compressedData) throws SerializationException {
        byte[] deflaterDecompressed = Compressor.deflaterDecompress(compressedData, false);
        return super.deserialize(deflaterDecompressed);
    }
}
