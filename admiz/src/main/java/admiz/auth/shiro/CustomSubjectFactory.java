package admiz.auth.shiro;

import admiz.common.auth.PrincipalTag;
import admiz.common.auth.UserDetailsProvider;
import admiz.system.model.SystemUser;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.subject.SubjectContext;
import org.apache.shiro.web.mgt.DefaultWebSubjectFactory;
import org.apache.shiro.web.subject.WebSubject;
import org.apache.shiro.web.subject.WebSubjectContext;

@Slf4j
@RequiredArgsConstructor
public class CustomSubjectFactory extends DefaultWebSubjectFactory {
    final UserDetailsProvider userDetailsProvider;

    public Subject createSubject(SubjectContext context) {
        //SHIRO-646
        //Check if the existing subject is NOT a WebSubject. If it isn't, then call super.createSubject instead.
        //Creating a WebSubject from a non-web Subject will cause the ServletRequest and ServletResponse to be null,
        // which wil fail when creating a session.
        boolean isNotBasedOnWebSubject = context.getSubject() != null && !(context.getSubject() instanceof WebSubject);
        if (!(context instanceof WebSubjectContext) || isNotBasedOnWebSubject) {
            return super.createSubject(context);
        }

        WebSubjectContext wsc = (WebSubjectContext) context;
        SecurityManager securityManager = wsc.resolveSecurityManager();
        Session session = wsc.resolveSession();
        boolean sessionEnabled = wsc.isSessionCreationEnabled();
        PrincipalCollection principals = wsc.resolvePrincipals(); // 这里实际上是个
        boolean authenticated = wsc.resolveAuthenticated();
        String host = wsc.resolveHost();
        ServletRequest request = wsc.resolveServletRequest();
        ServletResponse response = wsc.resolveServletResponse();


        CustomShiroSubject subject = new CustomShiroSubject(principals, authenticated, host, session, sessionEnabled, request, response, securityManager);

        if(principals != null) {
            PrincipalTag principalTag = (PrincipalTag) principals.getPrimaryPrincipal();
            SystemUser user = userDetailsProvider.getUser(principalTag.userId(), principalTag.realmName());
            subject.setCurrentUser(user);
        }

        return subject;
    }


}
