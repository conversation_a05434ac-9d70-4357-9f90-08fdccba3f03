package admiz.auth.shiro;


import admiz.errorhandling.Compressor;
import org.apache.shiro.lang.io.DefaultSerializer;
import org.apache.shiro.lang.io.SerializationException;

import java.util.zip.Deflater;

public class CustomSerializerTest<T> extends DefaultSerializer<T> {
//    final static LZ4Factory lz4Factory = LZ4Factory.fastestInstance();

    public byte[] serialize(T o) throws SerializationException {
        byte[] serialized = super.serialize(o);


        long startTime = System.nanoTime();
        byte[] compressedData = Compressor.deflaterCompress(serialized, Deflater.BEST_COMPRESSION, false);
        long endTime = System.nanoTime();
        System.out.printf("serialize time: BEST_COMPRESSION: %d size=%d %n", (endTime - startTime), compressedData.length);

        startTime = System.nanoTime();
        compressedData = Compressor.deflaterCompress(serialized, Deflater.DEFLATED, false);
        endTime = System.nanoTime();
        System.out.printf("serialize time: DEFLATED: %d size=%d %n", (endTime - startTime), compressedData.length);

        startTime = System.nanoTime();
        compressedData = Compressor.deflaterCompress(serialized, Deflater.DEFAULT_COMPRESSION, false);
        endTime = System.nanoTime();
        System.out.printf("serialize time: DEFAULT_COMPRESSION: %d size=%d %n", (endTime - startTime), compressedData.length);

        startTime = System.nanoTime();
        compressedData = Compressor.deflaterCompress(serialized, Deflater.BEST_SPEED, false);
        endTime = System.nanoTime();
        System.out.printf("serialize time: BEST_SPEED: %d size=%d %n", (endTime - startTime), compressedData.length);

        startTime = System.nanoTime();
        compressedData = Compressor.gzipCompress(serialized);
        endTime = System.nanoTime();
        System.out.printf("serialize time: gzipCompressed: %d size=%d %n", (endTime - startTime), compressedData.length);


        return compressedData;
    }

    public T deserialize(byte[] compressedData) throws SerializationException {
//        LZ4SafeDecompressor decompressor = lz4Factory.safeDecompressor();
//        byte[] serializedData = decompressor.decompress(compressedData, compressedData.length);

        return super.deserialize(compressedData);
    }
}
