package admiz;

import admiz.common.setting.ApplicationSetting;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ConfigurableApplicationContext;

@SpringBootApplication(scanBasePackages = {"admiz"})
@EnableConfigurationProperties({ApplicationSetting.class})
public class AdmizApp {
    public static void main(String[] args) {
        ConfigurableApplicationContext applicationContext = SpringApplication.run(AdmizApp.class, args);
    }

}
