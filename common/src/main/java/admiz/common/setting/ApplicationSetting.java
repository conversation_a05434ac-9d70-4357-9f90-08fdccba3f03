package admiz.common.setting;

import lombok.Data;
import org.apache.logging.log4j.util.Strings;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
@ConfigurationProperties(prefix = "application", ignoreUnknownFields = false)
public class ApplicationSetting {
    private String environment = "prod";
    private String enabledModules;
    private List<String> enabledModuleList = new ArrayList<>();
    private AuthenticationSetting authentication;

    public List<String> enabledModules() {

        if(Strings.isBlank(enabledModules)) {
            enabledModules = "admiz.module.core.SystemCoreModule";
        }

        enabledModuleList = Arrays.stream(enabledModules.split(","))
                .map(String::trim)
                .toList();

        return enabledModuleList;
    }

    public boolean isDevEnvironment() {
        return "dev".equals(environment);
    }

    public boolean isProdEnvironment() {
        return "prod".equals(environment);
    }
}
