package admiz.common.setting;

import com.fasterxml.jackson.databind.node.TextNode;

public class PreferenceItem {
    final String key;
    final TextNode value;
    final String description;

    public static PreferenceItem of(String key, String value, String description) {
        return new PreferenceItem(key, new TextNode(value), description);
    }

    private PreferenceItem(String key, TextNode value, String description) {
        this.key = key;
        this.value = value;
        this.description = description;
    }

    public String key() {
        return key;
    }
    public TextNode value() {
        return value;
    }
    public String description() {
        return description;
    }
}
