package admiz.common.setting;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AuthPreferences {

//    public static PreferenceItem MAX_FAILURE_ATTEMPTS = PreferenceItem.of("MAX_FAILURE_ATTEMPTS", "10", "");
    MAX_FAILURE_ATTEMPTS("10", "");

    final PreferenceItem item;

    AuthPreferences(String value, String description) {
        this.item = PreferenceItem.of(this.name(), value, description);
    }

    PreferenceItem preferenceItem() {
        return item;
    }
}
