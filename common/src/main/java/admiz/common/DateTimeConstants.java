package admiz.common;

import org.springframework.format.annotation.DateTimeFormat;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;

public class DateTimeConstants {
    public static final DateTimeFormatter yyyy_MM_ddThh_mm = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'hh:mm");
    public static final DateTimeFormatter yyyy_MM_dd_hh_mm_ss = DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm:ss");
}
