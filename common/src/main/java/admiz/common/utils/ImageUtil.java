package admiz.common.utils;

import javax.imageio.ImageIO;
import java.awt.image.RenderedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.UncheckedIOException;
import java.util.Base64;

public class ImageUtil
{
    public final static String IMAGE_FORMAT_JPG = "jpg";
    public final static String IMAGE_FORMAT_PMG = "png";

    public static String toBase64String(RenderedImage captchaImage) {
        return toBase64String(captchaImage, IMAGE_FORMAT_JPG);
    }

    public static String toDataURI(RenderedImage captchaImage) {
        return toDataURI(captchaImage, IMAGE_FORMAT_JPG);
    }

    public static String toDataURI(RenderedImage captchaImage, String imageFormat) {
        // data:image/png;base64,DATA
        String base64 = toBase64String(captchaImage, imageFormat);
        return "data:image/%s;%s".formatted(imageFormat, base64);
    }


    public static String toBase64String(RenderedImage captchaImage, String imageFormat) {
        byte[] bytes = toByteArray(captchaImage, imageFormat);
        return Base64.getEncoder().encodeToString(bytes);
    }

    public static byte[] toByteArray(RenderedImage captchaImage) {
        return toByteArray(captchaImage, IMAGE_FORMAT_JPG);
    }
    public static byte[] toByteArray(RenderedImage captchaImage, String imageFormat) {
        final ByteArrayOutputStream os = new ByteArrayOutputStream();

        try {
            ImageIO.write(captchaImage, imageFormat, os);
            return os.toByteArray();
        } catch (final IOException ioe) {
            throw new UncheckedIOException(ioe);
        }
    }

}
