package admiz.common.i18n;

import org.springframework.context.MessageSource;

import java.util.Locale;
import java.util.Objects;

public class I18n {
    static class I81nThreadLocal extends ThreadLocal<Locale> {
        @Override
        protected Locale initialValue() {
            return Locale.CHINA;
        }
    }

    private static MessageSource MESSAGE_SOURCE;
    private static I81nThreadLocal LOCALE_THREAD_LOCAL = new I81nThreadLocal();

    public static String $L(String key, Object... args) {
        Locale locale = LOCALE_THREAD_LOCAL.get();
        return $L(locale, key, args);
    }

    public static String $L(Locale locale, String key, Object... args) {
        Objects.requireNonNull(locale, "locale require not null");
        Objects.requireNonNull(key, "key require not null");
        return MESSAGE_SOURCE.getMessage(key, args, key, locale);
    }

    public static void setMessageSource(MessageSource messageSource) {
        MESSAGE_SOURCE = messageSource;
    }
}
