package admiz.common.auth;

import org.apache.shiro.authc.credential.DefaultPasswordService;
import org.apache.shiro.authc.credential.PasswordService;
import org.apache.shiro.crypto.hash.DefaultHashService;

public interface SecurityMixin {
    default PasswordService getPasswordService() {
        final DefaultPasswordService passwordService = new DefaultPasswordService();
        DefaultHashService hashService = new DefaultHashService();
        hashService.setDefaultAlgorithmName("2y");
        passwordService.setHashService(hashService);

        return passwordService;
    }
}
