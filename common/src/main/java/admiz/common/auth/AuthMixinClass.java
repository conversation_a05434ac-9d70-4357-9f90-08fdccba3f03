// package admiz.common.auth;
//
// import admiz.system.model.SystemUser;
// import org.apache.shiro.SecurityUtils;
//
// public class AuthMixinClass {
//     public boolean isAuthenticated() {
//         return SecurityUtils.getSubject().isAuthenticated();
//     }
//
//     public boolean isRememberMe() {
//         return SecurityUtils.getSubject().isRemembered();
//     }
//
//     public boolean isRunAs() {
//         return SecurityUtils.getSubject().isRunAs();
//     }
//
//     public boolean hasRole(String role) {
//         return SecurityUtils.getSubject().hasRole(role);
//     }
//
//     public boolean isPermitted(String permission) {
//         return SecurityUtils.getSubject().isPermitted(permission);
//     }
//
//     public SystemUser currentUser() {
//         return (SystemUser) SecurityUtils.getSubject().getPrincipal();
//     }
//     public String sessionId() {
//         return SecurityUtils.getSubject().getSession().getId().toString();
//     }
// }
