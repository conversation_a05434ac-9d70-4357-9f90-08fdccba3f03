//package admiz.common.auth;
//
//import admiz.system.model.SystemUser;
//import org.apache.shiro.SecurityUtils;
//
//public interface AuthMixin {
////    default final static AuthMixin INSTANCE = new AuthMixin();
////    private static ApplicationContext applicationContext;
////
////    default static void initialize(ApplicationContext applicationContext) {
////        AuthContext.applicationContext = applicationContext;
////    }
//
//    default boolean isAuthenticated() {
//        return SecurityUtils.getSubject().isAuthenticated();
//    }
//
//    default boolean isRememberMe() {
//        return SecurityUtils.getSubject().isRemembered();
//    }
//
//    default boolean isRunAs() {
//        return SecurityUtils.getSubject().isRunAs();
//    }
//
//    default boolean hasRole(String role) {
//        return SecurityUtils.getSubject().hasRole(role);
//    }
//
//    default boolean isPermitted(String permission) {
//        return SecurityUtils.getSubject().isPermitted(permission);
//    }
//
//    default SystemUser currentUser() {
//        return (SystemUser) SecurityUtils.getSubject().getPrincipal();
//    }
//    default String sessionId() {
//        return SecurityUtils.getSubject().getSession().getId().toString();
//    }
//
////    default UserInfoVO getUserInfo() {
////        if(!isAuthenticated() && !isRememberMe()) {
////            throw AuthErrors.AUTHENTICATION_REQUIRED.toException();
////        }
////
////        SystemUser systemUser = currentUser();
////
////        List<String> roleNameList = applicationContext.getBean(SystemRoleRepository.class).findUserRoles(systemUser.id()).stream().map(SystemRole::name).toList();
////        SystemResourceFetcher fetcher = Fetchers.SYSTEM_RESOURCE_FETCHER
////                .name()
////                .displayName()
////                .parentId()
////                .icon()
////                .resourceType()
////                .sortNum()
////                .target()
////                .url();
////
////        List<SystemResource> resourceList = applicationContext.getBean(SystemResourceRepository.class).findUserResources(systemUser.id(), fetcher);
////
////        UserInfoVO userInfoVO = new UserInfoVO(systemUser, roleNameList, resourceList);
////
////        return userInfoVO;
////    }
//}
