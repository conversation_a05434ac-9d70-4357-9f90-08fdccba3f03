package admiz.common.objectMapper;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import lombok.SneakyThrows;
import org.babyfish.jimmer.jackson.ImmutableModule;
import org.jetbrains.annotations.NotNull;

import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.TimeZone;

public class SimpleObjectMapper extends ObjectMapper {
    public static final SimpleObjectMapper SHARED_MAPPER;

    static {
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(dateTimeFormatter));
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(dateTimeFormatter));
        javaTimeModule.addSerializer(LocalDate.class, new LocalDateSerializer(dateFormatter));
        javaTimeModule.addDeserializer(LocalDate.class, new LocalDateDeserializer(dateFormatter));
        javaTimeModule.addSerializer(LocalTime.class, new LocalTimeSerializer(timeFormatter));
        javaTimeModule.addDeserializer(LocalTime.class, new LocalTimeDeserializer(timeFormatter));

        SHARED_MAPPER = (SimpleObjectMapper) new SimpleObjectMapper()
                .configure(DeserializationFeature.FAIL_ON_IGNORED_PROPERTIES, false)
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .registerModule(new ImmutableModule())
                .registerModule(javaTimeModule).setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
    }


    @SneakyThrows
    @Override
    public String writeValueAsString(Object value) {
        return super.writeValueAsString(value);
    }

    @SneakyThrows
    @NotNull
    @Override
    public byte[] writeValueAsBytes(Object value)  {
        return super.writeValueAsBytes(value);
    }

    @SneakyThrows
    @Override
    public JsonNode readTree(byte[] content)  {
        return super.readTree(content);
    }

    @SneakyThrows
    @Override
    public JsonNode readTree(String content)  {
        return super.readTree(content);
    }

    @SneakyThrows
    @Override
    public <T> T readValue(String content, Class<T> valueType) {
        return super.readValue(content, valueType);
    }

    @SneakyThrows
    @Override
    public <T> T readValue(byte[] src, Class<T> valueType)  {
        return super.readValue(src, valueType);
    }

    @SneakyThrows
    @Override
    public <T> T readValue(InputStream src, Class<T> valueType)  {
        return super.readValue(src, valueType);
    }
}
