package admiz.common.result;

import admiz.common.errorhandling.ErrorDetail;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
public class ResultPaged<T> extends Result<T> {
	private static final long serialVersionUID = 2746164828180904542L;

	protected long total;

	public ResultPaged(ErrorDetail resultCode, String msg, T data, long total) {
		super(resultCode, msg, data);
		this.total = total;
	}
}
