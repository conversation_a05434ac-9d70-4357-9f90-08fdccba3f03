package admiz.common.result;

import admiz.common.errorhandling.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.springframework.data.domain.Page;
import org.springframework.lang.Nullable;
import org.springframework.validation.BindingResult;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static admiz.common.i18n.I18n.$L;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Accessors(chain = true, fluent = true)
@ToString
public class Result<T> implements Serializable {
    private static final long serialVersionUID = -6713404394935683645L;

    @JsonProperty
    protected int code = -1;

    @JsonProperty
    protected boolean succeed = false;

    @JsonProperty
    protected String msg = null;

    @JsonProperty
    protected T data = null;

    @JsonProperty
    protected T error = null;

    public Result(int errorCode, String msg) {
        this(errorCode, msg, null);
    }

    public Result(ErrorDetail resultCode) {
        this(resultCode, resultCode.getLocalizedMessage(), null);
    }

    public Result(ErrorDetail resultCode, String msg) {
        this(resultCode, msg, null);
    }

    public Result(ErrorDetail resultCode, T data) {
        this(resultCode, resultCode.getLocalizedMessage(), data);
    }

    public Result(ErrorDetail resultCode, String msg, T data) {
        this(resultCode.getCode(), msg, data);
    }

    public Result(int errorCode, String msg, T data) {
        this(errorCode, msg, data, null);
    }

    public Result(int errorCode, String msg, T data, T error) {
        this.code = errorCode;
        this.msg = msg;
        this.data = data;
        this.succeed = AppErrors.CommonError.SUCCESS.getCode() == errorCode;
        this.error = error;
    }

    /**
     * 判断返回是否为成功
     *
     * @return 是否成功
     */
    public boolean succeed() {
        return AppErrors.CommonError.SUCCESS.getCode() == code;
    }

    /**
     * 判断返回是否为成功
     *
     * @return 是否成功
     */
    @JsonIgnore
    public boolean isNotSucceed() {
        return !succeed();
    }


    // 静态方法

    /**
     * 返回成功，使用默认成功消息
     */
    public static <T> Result<T> ok() {
        return new Result<T>(AppErrors.CommonError.SUCCESS, (String) null); // 不指定成功消息的时候，无需返回信息给调用者
    }

    /**
     * 返回成功
     *
     * @param message 成功消息
     * @return Result
     */
    public static <T> Result<T> ok(String message) {
        return new Result<T>(AppErrors.CommonError.SUCCESS, message);
    }

    /**
     * 成功-携带数据
     *
     * @param data 数据
     * @param <T>  泛型标记
     * @return Result
     */
    public static <T> Result<T> data(@Nullable T data) {
        return new Result<>(AppErrors.CommonError.SUCCESS, data);
    }

    /**
     * 失败-返回参数检验错误
     *
     * @param errorResult 数据
     * @return Result
     */
    public static Result<?> error(BindingResult errorResult) {
        Map<String, String> errors = new HashMap<>();
        errorResult.getFieldErrors().forEach(error -> errors.put(error.getField(), $L(error.getDefaultMessage())));

        return error(AppErrors.CommonError.INVALID_PARAMETER, errors);
    }

    /**
     * 失败-携带数据
     *
     * @param error 数据
     * @return Result
     */
    public static Result<?> error(@Nullable Object error) {
        return error(AppErrors.CommonError.FAILURE, error);
    }


    /**
     * 失败-携带数据
     *
     * @param error 数据
     * @return Result
     */
    public static Result<?> error(ErrorDetail error, @Nullable Object errorInfo) {
        return new Result<>(error.getCode(), false, error.getLocalizedMessage(), null, errorInfo);
    }

    /**
     * 成功-携带数据
     *
     * @param pagedData 数据
     * @param <T>       泛型标记
     * @return Result
     */
    public static <T> Result<List<T>> page(@Nullable Page<T> pagedData) {
        assert pagedData != null;
        return new ResultPaged<List<T>>(AppErrors.CommonError.SUCCESS, AppErrors.CommonError.SUCCESS.getLocalizedMessage(), pagedData.getContent(), pagedData.getTotalElements());
    }

    /**
     * 成功-返回分页数据
     *
     * @param data 数据
     * @param <T>  泛型标记
     * @return Result
     */
    public static <T> ResultPaged<T> page(@Nullable T data, long total) {
        return new ResultPaged<T>(AppErrors.CommonError.SUCCESS, AppErrors.CommonError.SUCCESS.getLocalizedMessage(), data, total);
    }

    /**
     * 返回失败信息，用于 web
     *
     * @param <T> 泛型标记
     * @return {Result}
     */
    public static <T> Result<T> fail() {
        return new Result<>(AppErrors.CommonError.FAILURE);
    }

    /**
     * 返回失败信息，用于 web
     *
     * @param msg 失败信息
     * @param <T> 泛型标记
     * @return {Result}
     */
    public static <T> Result<T> fail(String msg) {
        return new Result<>(AppErrors.CommonError.FAILURE, msg);
    }

    //	/**
    //	 * 返回失败信息
    //	 *
    //	 * @param resultCode 异常枚举
    //	 * @param <T>   泛型标记
    //	 * @return {Result}
    //	 */
    //	public static <T> Result<T> fail(ErrorCodeInterface resultCode) {
    //		return new Result<>(resultCode);
    //	}
    //
    //	/**
    //	 * 返回失败信息
    //	 *
    //	 * @param resultCode 异常枚举
    //	 * @param msg   失败信息
    //	 * @param <T>   泛型标记
    //	 * @return {Result}
    //	 */
    //	public static <T> Result<T> fail(ErrorCodeInterface resultCode, String msg) {
    //		return new Result<>(resultCode, msg);
    //	}

    /**
     * 返回失败信息
     *
     * @param msg 失败信息
     * @param <T> 泛型标记
     * @return {Result}
     */
    public static <T> Result<T> fail(int errorCode, String msg) {
        return new Result<>(errorCode, msg);
    }


    /**
     * 当 succeed 不为 true 时：直接抛出失败异常 resultCode
     *
     * @param succeed    succeed
     * @param resultCode 异常枚举
     */
    public static void throwOnFail(boolean succeed, ErrorDetail resultCode) throws ErrorDetailException {
        if (!succeed) {
            throw resultCode.toException();
        }
    }

    /**
     * 当 succeed 不为 true 时：直接抛出失败异常 resultCode、message
     *
     * @param succeed    succeed
     * @param resultCode 异常枚举
     * @param msg        失败信息
     */
    public static void throwOnFail(boolean succeed, ErrorDetail resultCode, String msg) throws ErrorDetailException {
        if (!succeed) {
            throw new ErrorDetailException(resultCode, msg);
        }
    }
}
