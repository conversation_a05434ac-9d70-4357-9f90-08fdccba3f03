package admiz.common.result;//package lyadmin.common.result;
//
//import lombok.ToString;
//import lyadmin.common.errorhandling.CommonErrors;
//import lyadmin.common.errorhandling.ErrorCodeInterface;
//import lyadmin.common.errorhandling.ServiceException;
//import org.springframework.data.domain.Page;
//import org.springframework.lang.Nullable;
//
//import java.io.Serializable;
//import java.util.List;
//
//@ToString
//public class R<T> implements Serializable {
//	/**
//	 * 返回成功，使用默认成功消息
//	 */
//	public static <T> Result<T> success() {
//		return new Result<T>(CommonErrors.SUCCESS, (String)null); // 不指定成功消息的时候，无需返回信息给调用者
//	}
//
//	/**
//	 * 返回成功
//	 *
//	 * @param message 成功消息
//	 * @return Result
//	 */
//	public static <T> Result<T> success(String message) {
//		return new Result<T>(CommonErrors.SUCCESS, message);
//	}
//
//	/**
//	 * 成功-携带数据
//	 *
//	 * @param data 数据
//	 * @param <T>  泛型标记
//	 * @return Result
//	 */
//	public static <T> Result<T> data(@Nullable T data) {
//		return new Result<>(CommonErrors.SUCCESS, data);
//	}
//
//
//	/**
//	 * 成功-携带数据
//	 *
//	 * @param pagedData 数据
//	 * @param <T>  泛型标记
//	 * @return Result
//	 */
//	public static <T> Result<List<T>> data(@Nullable Page<T> pagedData) {
//		assert pagedData != null;
//		return new ResultPaged<List<T>>(CommonErrors.SUCCESS, CommonErrors.SUCCESS.getMessage(), pagedData.getContent(), pagedData.getTotalElements());
//	}
//
//	/**
//	 * 成功-返回分页数据
//	 *
//	 * @param data 数据
//	 * @param <T>  泛型标记
//	 * @return Result
//	 */
//	public static <T> ResultPaged<T> data(@Nullable T data, long total) {
//		return new ResultPaged<T>(CommonErrors.SUCCESS, CommonErrors.SUCCESS.getMessage(), data, total);
//	}
//
//	/**
//	 * 返回失败信息，用于 web
//	 *
//	 * @param <T> 泛型标记
//	 * @return {Result}
//	 */
//	public static <T> Result<T> fail() {
//		return new Result<>(CommonErrors.FAILURE);
//	}
//
//	/**
//	 * 返回失败信息，用于 web
//	 *
//	 * @param msg 失败信息
//	 * @param <T> 泛型标记
//	 * @return {Result}
//	 */
//	public static <T> Result<T> fail(String msg) {
//		return new Result<>(CommonErrors.FAILURE, msg);
//	}
//
//	/**
//	 * 返回失败信息
//	 *
//	 * @param resultCode 异常枚举
//	 * @param <T>   泛型标记
//	 * @return {Result}
//	 */
//	public static <T> Result<T> fail(ErrorCodeInterface resultCode) {
//		return new Result<>(resultCode);
//	}
//
//	/**
//	 * 返回失败信息
//	 *
//	 * @param resultCode 异常枚举
//	 * @param msg   失败信息
//	 * @param <T>   泛型标记
//	 * @return {Result}
//	 */
//	public static <T> Result<T> fail(ErrorCodeInterface resultCode, String msg) {
//		return new Result<>(resultCode, msg);
//	}
//
//	/**
//	 * 返回失败信息
//	 *
//	 * @param msg   失败信息
//	 * @param <T>   泛型标记
//	 * @return {Result}
//	 */
//	public static <T> Result<T> fail(int errorCode, String msg) {
//		return new Result<>(errorCode, msg);
//	}
//
////	/**
////	 * 当 result 不成功时：直接抛出失败异常，返回传入的 result。
////	 *
////	 * @param result ApiResult
////	 */
////	public static void throwOnFail(ApiResult<?> result) {
////		if (ApiResult.isNotSuccess(result)) {
////			throw new ServiceException(result.code, result.msg);
////		}
////	}
//
//	/**
//	 * 当 result 不成功时：直接抛出失败异常，返回传入的 resultCode
//	 *
//	 * @param result ApiResult
//	 * @param resultCode 异常枚举
//	 */
//	public static void throwOnFail(Result<?> result, ErrorCodeInterface resultCode) throws ServiceException {
//		if (result.isNotSucceed()) {
//			throw new ServiceException(resultCode);
//		}
//	}
//
//	/**
//	 * 当 result 不成功时：直接抛出失败异常，返回传入的 resultCode、message
//	 *
//	 * @param result ApiResult
//	 * @param resultCode 异常枚举
//	 * @param msg 失败信息
//	 */
//	public static void throwOnFail(Result<?> result, ErrorCodeInterface resultCode, String msg) throws ServiceException {
//		if (result.isNotSucceed()) {
//			throw new ServiceException(resultCode, msg);
//		}
//	}
//
//	/**
//	 * 当 status 不为 true 时：直接抛出失败异常 resultCode
//	 *
//	 * @param status status
//	 * @param resultCode 异常枚举
//	 */
//	public static void throwOnFalse(boolean status, ErrorCodeInterface resultCode) throws ServiceException {
//		if (!status) {
//			throw new ServiceException(resultCode);
//		}
//	}
//
//	/**
//	 * 当 status 不为 true 时：直接抛出失败异常 resultCode、message
//	 *
//	 * @param status status
//	 * @param resultCode 异常枚举
//	 * @param msg 失败信息
//	 */
//	public static void throwOnFalse(boolean status, ErrorCodeInterface resultCode, String msg) throws ServiceException {
//		if (!status) {
//			throw new ServiceException(resultCode, msg);
//		}
//	}
//
//	/**
//	 * 直接抛出失败异常，抛出 code 码
//	 *
//	 * @param resultCode IResultCode
//	 */
//	public static void throwFail(ErrorCodeInterface resultCode) throws ServiceException {
//		throw new ServiceException(resultCode);
//	}
//
//	/**
//	 * 直接抛出失败异常，抛出 code 码
//	 *
//	 * @param resultCode   IResultCode
//	 * @param message 自定义消息
//	 */
//	public static void throwFail(ErrorCodeInterface resultCode, String message) throws ServiceException {
//		throw new ServiceException(resultCode, message);
//	}
//}
