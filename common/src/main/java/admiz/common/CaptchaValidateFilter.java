package admiz.common;//package lyadmin.common;
//
//import com.google.code.kaptcha.Constants;
//import jakarta.servlet.ServletRequest;
//import jakarta.servlet.ServletResponse;
//import jakarta.servlet.http.HttpServletRequest;
//import lombok.RequiredArgsConstructor;
//import lombok.Setter;
//import lyadmin.system.authn.shiro.ShiroConstants;
//import org.apache.logging.log4j.util.Strings;
//import org.apache.shiro.SecurityUtils;
//import org.apache.shiro.web.filter.AccessControlFilter;
//import org.springframework.core.env.Environment;
//import org.springframework.core.env.Profiles;
//
///**
// * 验证码过滤器
// *
// */
//@RequiredArgsConstructor
//public class CaptchaValidateFilter extends AccessControlFilter
//{
//    final Environment env;
//
//    /**
//     * 是否开启验证码
//     */
//    @Setter
//    private boolean captchaEnabled = true;
//
//    /**
//     * 验证码类型
//     */
//    @Setter
//    private String captchaType = "math";
//
//    @Override
//    public boolean onPreHandle(ServletRequest request, ServletResponse response, Object mappedValue) throws Exception
//    {
//        request.setAttribute(ShiroConstants.CURRENT_ENABLED, captchaEnabled);
//        request.setAttribute(ShiroConstants.CURRENT_TYPE, captchaType);
//        return super.onPreHandle(request, response, mappedValue);
//    }
//
//    @Override
//    protected boolean isAccessAllowed(ServletRequest request, ServletResponse response, Object mappedValue)
//            throws Exception
//    {
//        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
//
//        // 验证码禁用 或不是表单提交 允许访问
//        if (!captchaEnabled || env.acceptsProfiles(Profiles.of("dev")) || !"post".equalsIgnoreCase(httpServletRequest.getMethod()))
//        {
//            return true;
//        }
//
//        return validateCaptcha(httpServletRequest);
//    }
//
//    @Override
//    protected boolean onAccessDenied(ServletRequest servletRequest, ServletResponse servletResponse) throws Exception {
//        return true;
//    }
//
//    // TODO: 感觉这里很可能有绕过验证码的BUG
//    public boolean validateCaptcha(HttpServletRequest request)
//    {
//        String validateCode = request.getParameter(ShiroConstants.CURRENT_VALIDATECODE);
//        Object obj = SecurityUtils.getSubject().getSession().getAttribute(Constants.KAPTCHA_SESSION_KEY);
//        request.getSession().removeAttribute(Constants.KAPTCHA_SESSION_KEY);
//
//        String code = obj == null ? "" : obj.toString();
//        boolean isValid = Strings.isNotBlank(validateCode) && validateCode.equalsIgnoreCase(code);
//        if(!isValid) {
//            // 验证码清除，防止多次使用。
//            request.setAttribute(ShiroConstants.CURRENT_CAPTCHA, ShiroConstants.CAPTCHA_ERROR);
//        }
//
//        return isValid;
//    }
//}
