package admiz.common.entity;

import lombok.extern.slf4j.Slf4j;
import org.babyfish.jimmer.ImmutableObjects;
import org.babyfish.jimmer.sql.DraftInterceptor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component @Slf4j
public class BaseEntityDraftInterceptor implements DraftInterceptor<BaseEntity, BaseEntityDraft> {
    @Override
    public void beforeSave(@NotNull BaseEntityDraft draft, @Nullable BaseEntity original) {
        log.info("BaseEntity: entityType={}", draft.getClass().getSimpleName());

        if (!ImmutableObjects.isLoaded(draft, BaseEntityProps.CREATE_TIME)) {
            draft.setCreateTime(LocalDateTime.now());
        }
        draft.setUpdateTime(LocalDateTime.now());
    }
}
