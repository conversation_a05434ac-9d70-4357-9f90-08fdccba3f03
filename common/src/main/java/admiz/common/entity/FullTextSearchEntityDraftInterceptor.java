// package admiz.common.entity;
//
// import lombok.extern.slf4j.Slf4j;
// import org.babyfish.jimmer.sql.DraftInterceptor;
// import org.jetbrains.annotations.NotNull;
// import org.jetbrains.annotations.Nullable;
// import org.springframework.stereotype.Component;
//
// import java.lang.reflect.InvocationTargetException;
// import java.lang.reflect.Method;
// import java.util.Optional;
//
// @Component @Slf4j
// public class FullTextSearchEntityDraftInterceptor implements DraftInterceptor<FullTextSearchBaseEntity, FullTextSearchBaseEntityDraft> {
//     @Override
//     public void beforeSave(@NotNull FullTextSearchBaseEntityDraft draft, @Nullable FullTextSearchBaseEntity original) {
//         try {
//             Method ftsMeta = original.getClass().getMethod("ftsMeta");
//             Object ret = ftsMeta.invoke(draft);
//         } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
//             log.error("", e);
//         }
//
//         Optional<FullTextSearchMeta<Long>> fullTextSearchMeta = original.ftsMeta();
//         if(fullTextSearchMeta.isEmpty()) {
//             return;
//         }
//
//         draft.setFullTextSearchContent(fullTextSearchMeta.get().content());
//     }
// }
