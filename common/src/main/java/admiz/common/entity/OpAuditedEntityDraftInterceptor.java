package admiz.common.entity;

import lombok.extern.slf4j.Slf4j;
import org.babyfish.jimmer.DraftObjects;
import org.babyfish.jimmer.ImmutableObjects;
import org.babyfish.jimmer.sql.DraftInterceptor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collection;
import java.util.Optional;

@Component @Slf4j
public class OpAuditedEntityDraftInterceptor implements DraftInterceptor<OpAuditedEntity, OpAuditedEntityDraft> {
    @Override
    public void beforeSave(OpAuditedEntityDraft draft, OpAuditedEntity original) {
        String entityPackage = draft.getClass().getPackageName();
        Class<?>[] entityInterfaces = draft.getClass().getSuperclass().getInterfaces();
        Optional<Class<?>> entityClass = Arrays.stream(entityInterfaces)
                .filter(x -> x.getPackageName().equals(entityPackage))
                .findFirst();
        if(entityClass.isEmpty()) {
            log.debug("Not matched entity class: {}", draft.getClass().getSimpleName());
            return;
        }

        Class<?> entityInterface = entityClass.get();
        if (ImmutableObjects.isLoaded(draft, "id")) {

        }
        log.info("OpAudited: entityType={}", entityInterface.getName());

        // DraftInterceptor.super.beforeSave(draft, original);
    }

    // @Override
    // public void beforeSaveAll(@NotNull Collection<Item<OpAuditedEntity, OpAuditedEntityDraft>> items) {
    //
    //     // DraftInterceptor.super.beforeSaveAll(items);
    // }
}
