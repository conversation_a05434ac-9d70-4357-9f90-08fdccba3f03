package admiz.common;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Arrays;

@RequiredArgsConstructor @Getter @Accessors(fluent = true)
public enum DataRealm {
    SYSTEM("系统内置"),
    USER("用户自定义"),
    ;

    private final String description;

    @JsonCreator
    public static DataRealm fromString(String strValue) {
        return Arrays.stream(DataRealm.values()).filter(x -> x.name().equals(strValue)).findFirst().orElse(SYSTEM);
    }
}
