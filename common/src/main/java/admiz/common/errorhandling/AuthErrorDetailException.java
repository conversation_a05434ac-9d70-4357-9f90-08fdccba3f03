package admiz.common.errorhandling;

import lombok.Getter;
import org.apache.shiro.authc.AuthenticationException;

import java.io.PrintWriter;
import java.io.StringWriter;

// 登录异常
@Getter
public class AuthErrorDetailException extends AuthenticationException {
    private static final long serialVersionUID = 1L;

    final private ErrorDetail errorDetail;
    final private ErrorTag tag;

    private transient String stackTraceString = null;

    public AuthErrorDetailException(ErrorDetail errorDetail) {
        this(errorDetail, errorDetail.getMessage(), null, null);
    }

    public AuthErrorDetailException(ErrorDetail errorDetail, String message) {
        this(errorDetail, message, null, null);
    }

    public AuthErrorDetailException(ErrorDetail errorDetail, String message, ErrorTag tag) {
        this(errorDetail, message, tag, null);
    }

    public AuthErrorDetailException(ErrorDetail errorDetail, String message, ErrorTag tag, Throwable cause) {
        super(message, cause);
        this.errorDetail = errorDetail;
        this.tag = tag;
    }

    public int getCode() {
        return errorDetail.getCode();
    }

    public synchronized String getStackTraceAsString() {
        if(stackTraceString == null) {
            StringWriter errors = new StringWriter();
            super.printStackTrace(new PrintWriter(errors));

            stackTraceString = errors.toString();
        }

        return stackTraceString;
    }
}
