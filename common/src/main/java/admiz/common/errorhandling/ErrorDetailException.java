package admiz.common.errorhandling;

import lombok.Getter;

import java.io.PrintWriter;
import java.io.StringWriter;

// 业务异常
@Getter
public class ErrorDetailException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    final private ErrorDetail errorDetail;
    final private ErrorTag tag;

    private transient String stackTraceString = null;

    public ErrorDetailException(ErrorDetail errorDetail) {
        this(errorDetail, errorDetail.getLocalizedMessage(), null, null);
    }

    public ErrorDetailException(ErrorDetail errorDetail, String message) {
        this(errorDetail, message, null, null);
    }

    public ErrorDetailException(ErrorDetail errorDetail, String message, ErrorTag tag) {
        this(errorDetail, message, tag, null);
    }

    public ErrorDetailException(ErrorDetail errorDetail, String message, ErrorTag tag, Throwable cause) {
        super(message, cause);
        this.errorDetail = errorDetail;
        this.tag = tag;
    }

    public synchronized String getStackTraceAsString() {
        if(stackTraceString == null) {
            StringWriter errors = new StringWriter();
            super.printStackTrace(new PrintWriter(errors));

            stackTraceString = errors.toString();
        }

        return stackTraceString;
    }
}
