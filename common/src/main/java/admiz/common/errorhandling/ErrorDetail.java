package admiz.common.errorhandling;

import admiz.common.result.Result;

import java.io.Serializable;

import static admiz.common.i18n.I18n.$L;

public interface ErrorDetail extends Serializable {

    int getCode();

    String getMessage();

    default String getLocalizedMessage() {
        return $L(getMessage());
    }

    default Result<?> toResult() {
        return new Result<>(this);
    }

    default RuntimeException toException() {
        return new ErrorDetailException(this);
    }

    default RuntimeException toException(ErrorTag tag) {
        return new ErrorDetailException(this, this.getLocalizedMessage(), tag);
    }

    default RuntimeException toException(String message) {
        return new ErrorDetailException(this, message);
    }

    default RuntimeException toException(String message, ErrorTag tag) {
        return new ErrorDetailException(this, message, tag);
    }

    // default ErrorDetail of(String name) {
    //     return ErrorDetailUtil.of(name);
    // }
    //
    // default ErrorDetail of(int code) {
    //     return ErrorDetailUtil.of(code);
    // }
}
