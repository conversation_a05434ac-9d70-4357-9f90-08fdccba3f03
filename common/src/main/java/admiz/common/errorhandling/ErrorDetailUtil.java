package admiz.common.errorhandling;

import org.reflections.Reflections;
import org.reflections.util.ConfigurationBuilder;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.locks.ReentrantLock;

public class ErrorDetailUtil {
    private static final ReentrantLock lock = new ReentrantLock();

    static volatile Object initFlag = null;
    static volatile Map<String, Enum<? extends ErrorDetail>> nameToEnumMap = null;
    static volatile Map<Integer, Enum<? extends ErrorDetail>> codeToEnumMap = null;

    public static ErrorDetail of(String name) {
        init();
        return (ErrorDetail)nameToEnumMap.get(name);
    }
    public static ErrorDetail of(int code) {
        init();
        return (ErrorDetail)codeToEnumMap.get(code);
    }

    public static Collection<Enum<? extends ErrorDetail>> findAllErrors() {
        return findAllErrors("");
    }

    public static Collection<Enum<? extends ErrorDetail>> findAllErrors(String inPackage) {
        init();
        return nameToEnumMap.values();
    }

    private static void init() {
        if (initFlag == null) { // 第一次检查
            lock.lock();
            try {
                if (initFlag == null) { // 第二次检查
                    initFlag = new Object();
                    nameToEnumMap = new HashMap<>();
                    codeToEnumMap = new HashMap<>();

                    Reflections reflections = new Reflections(new ConfigurationBuilder().forPackage("admiz"));
                    // Reflections reflections = new Reflections();
                    Set<Class<? extends ErrorDetail>> classSet = reflections.getSubTypesOf(ErrorDetail.class);

                    for (Class<?> type: classSet) {
                        if(!type.isEnum()) {
                            continue;
                        }

                        Object[] enumConstants = type.getEnumConstants();
                        for (Object en: enumConstants) {
                            String name = ((Enum) en).name();
                            Integer code = ((ErrorDetail) en).getCode();
                            Enum<? extends ErrorDetail> errorDetail = (Enum<? extends ErrorDetail>)en;

                            if(nameToEnumMap.containsKey(name) || codeToEnumMap.containsKey(code) ) {
                                Enum<? extends ErrorDetail> existingName = nameToEnumMap.get(name);
                                String errorMsgFmt = "Duplicated Error Enum, both defined in \"%s.%s(code=%d)\" and \"%s.%s(code=%d)\"";
                                String formatted = errorMsgFmt.formatted(existingName.getDeclaringClass().getName(), existingName.name(), ((ErrorDetail)existingName).getCode(),
                                        errorDetail.getDeclaringClass().getName(), errorDetail.name(), ((ErrorDetail)errorDetail).getCode());
                                throw new RuntimeException(formatted);
                            }

                            nameToEnumMap.put(name, errorDetail);
                            codeToEnumMap.put(code, errorDetail);
                        }
                    }
                }
            } finally {
                lock.unlock();
            }
        }
    }
}
