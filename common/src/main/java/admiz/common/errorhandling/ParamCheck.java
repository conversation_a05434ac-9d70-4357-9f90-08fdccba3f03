package admiz.common.errorhandling;

import com.google.common.base.Strings;

import java.util.Objects;

import static admiz.common.i18n.I18n.$L;

public class ParamCheck
{
    public static <T> T requireNonNull(T paramValue) {
        return requireNonNull(paramValue, null, null);
    }
    public static <T> T requireNonNull(T paramValue, String message) {
        return requireNonNull(paramValue, message, null);
    }
    public static <T> T requireNonNull(T paramValue, String message, String paramName) {
        if (paramValue == null)
            throw AppErrors.CommonError.INVALID_PARAMETER.toException($L(message), ErrorTag.of(paramName, paramValue));

        return paramValue;
    }

    public static void requireNonBlank(String paramValue) {
        requireNonBlank(paramValue, null, null);
    }
    public static void requireNonBlank(String paramValue, String message) {
        requireNonBlank(paramValue, message, null);
    }
    public static void requireNonBlank(String paramValue, String message, String paramName) {
        if (Strings.isNullOrEmpty(paramValue))
            throw AppErrors.CommonError.INVALID_PARAMETER.toException($L(message), ErrorTag.of(paramName, paramValue));
    }
    public static void requires(boolean satisfied, String message, String paramName) {
        if (!satisfied)
            throw AppErrors.CommonError.INVALID_PARAMETER.toException($L(message), ErrorTag.of(paramName, null));
    }
    public static void requiresEquals(String paramValue1, String paramValue2, String message, String paramName) {
        if (!Objects.equals(paramValue1, paramValue2))
            throw AppErrors.CommonError.INVALID_PARAMETER.toException($L(message), ErrorTag.of(paramName, paramValue1));
    }
}
