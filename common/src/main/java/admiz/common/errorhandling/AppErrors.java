package admiz.common.errorhandling;

import lombok.AllArgsConstructor;
import lombok.Getter;

public class AppErrors  {
    @Getter
    @AllArgsConstructor
    public static enum CommonError implements ErrorDetail {
        SUCCESS(0, "操作成功"),
        FAILURE(-1, "系统未知异常"),

        INVALID_PARAMETER(100110, "参数格式错误"),
        INVALID_SIGNATURE(100230, "参数签名校验失败"),

        UNAUTHENTICATED(100401, "用户未登录"),
        BAD_REQUEST(100402, "非法请求"),
        FORBIDDEN(100403, "请求被拒绝"),
        NOT_FOUND(100404, "没找到请求的数据"),
        METHOD_NOT_SUPPORTED(100405, "不支持当前请求方法"),
        CONCURRENCY_FAILURE(100409, "并发错误"),
        SERVER_ERROR(100500, "服务器错误"),
        CONFLICT(100501, "资源冲突"),
        INTERNAL_SERVER_ERROR(100502, "服务器内部错误");

        final int code;
        final String message;
    }

    // @Getter
    // @AllArgsConstructor
    // public static enum ParamError implements ErrorDetail {
    //     PARAMETER_MISSING(100100, "缺少必要的请求参数"),
    //     INVALID_PASSWORD(100210, "帐号或密码错误"),
    //     INVALID_CAPTCHA(100220, "验证码不正确"),
    //     INVALID_SIGNATURE(100230, "参数签名校验失败"),
    //     ;
    //
    //     final int code;
    //     final String message;
    // }


    @Getter
    @AllArgsConstructor
    public enum Auth implements AuthErrorDetail {
        INVALID_USERNAME(200100, "账号不正确"),
        INCORRECT_CAPTCHA(200120, "验证码不正确"),
        INCORRECT_CREDENTIALS(200401, "帐号或密码错误"),
        ACCOUNT_NOT_FOUND(200404, "帐号不存在"),
        ACCOUNT_LOCKED(200410, "帐号已锁定"),
        AUTHENTICATION_REQUIRED(200401, "需要登录"),
        ACCESS_DENIED(200403, "权限不足"),
        ;

        final int code;
        final String message;
    }
}
