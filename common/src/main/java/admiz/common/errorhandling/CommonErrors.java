package admiz.common.errorhandling;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CommonErrors implements ErrorDetail {
    SUCCESS(0, "操作成功"),
    FAILURE(-1, "系统未知异常"),

    PARAMETER_MISSING(100100, "缺少必要的请求参数"),
    INVALID_PARAMETER(100200, "参数数据错误"),
    INVALID_SIGNATURE(100300, "参数签名校验失败"),
    INVALID_CAPTCHA(100210, "服务器错误"),
    BAD_REQUEST(100402, "非法请求"),
    FORBIDDEN(100403, "请求被拒绝"),
    NOT_FOUND(100404, "没找到请求的数据"),
    METHOD_NOT_SUPPORTED(100405, "不支持当前请求方法"),
    CONCURRENCY_FAILURE(100409, "并发错误"),
    SERVER_ERROR(100500, "服务器错误"),
    ;

    final int code;
    final String message;
}
