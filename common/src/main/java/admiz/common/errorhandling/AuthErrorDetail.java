package admiz.common.errorhandling;

public interface AuthErrorDetail extends ErrorDetail {

    @Override
    default RuntimeException toException() {
        return new AuthErrorDetailException(this);
    }

    @Override
    default RuntimeException toException(String message) {
        return new AuthErrorDetailException(this, message);
    }

    @Override
    default RuntimeException toException(ErrorTag tag) {
        return new AuthErrorDetailException(this, this.getMessage(), tag);
    }

    @Override
    default RuntimeException toException(String message, ErrorTag tag) {
        return new AuthErrorDetailException(this, message, tag);
    }

}
