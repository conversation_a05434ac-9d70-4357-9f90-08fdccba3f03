package admiz.common.errorhandling;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.reflections.Reflections;
import org.reflections.util.ConfigurationBuilder;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.locks.ReentrantLock;

@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class ErrorTag {
    final String tagName;
    final Object tag;

    public static ErrorTag of(String tagName, Object tag) {
        return new ErrorTag(tagName, tagName);
    }
}
