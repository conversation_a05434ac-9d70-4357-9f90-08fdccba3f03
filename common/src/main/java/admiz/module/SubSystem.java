package admiz.module;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

@RequiredArgsConstructor
public enum SubSystem {
    MANAGEMENT_CENTER("管理中心"),
    TEST_SUBSYSTEM("测试子系统"),
    ;

    @JsonProperty
    final String displayName;

    public String displayName() {
        return displayName;
    }

    @JsonCreator
    public static SubSystem fromString(String name) {
        return Arrays.stream(SubSystem.values()).filter(x -> x.name().equals(name)).findFirst().orElse(MANAGEMENT_CENTER);
    }
}
