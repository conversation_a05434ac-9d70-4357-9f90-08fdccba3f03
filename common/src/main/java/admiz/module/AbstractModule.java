package admiz.module;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public abstract class AbstractModule implements Module {
    @JsonProperty("module_info")
    ModuleProperties properties;
    @JsonProperty("menus")
    List<ModuleMenu> menus;

    @Override
    public void initialize() {
    }

    @Override
    public String id() {
        return getClass().getSimpleName();
    }

    @Override
    public String name() {
        return properties.getName();
    }

    @Override
    public String description() {
        return properties.getName();
    }

    @Override
    public SubSystem subSystem() {
        return properties.getSubSystem();
    }

    @Override
    public boolean enabled() {
        return properties.isEnabled();
    }

    @Override
    public int order() {
        return properties.getOrder();
    }

    @Override
    public List<ModuleMenu> menus() {
        return menus;
    }

//    protected void loadModule(String name) {
//        if(properties == null) {
//            String resourceName = "module." + name + ".yaml";
//            ObjectMapper mapper = new ObjectMapper(new YAMLFactory());
//
//            try {
//
//                Module module = mapper.readValue(new ClassPathResource(resourceName).getInputStream(), this.getClass());
//                JsonNode root = mapper.readTree(new ClassPathResource(resourceName).getFile());
//                ModuleProperties properties = mapper.readValue(root.get("module_info").asText(), ModuleProperties.class);
//                List<ModuleMenu> fruits = mapper.readValue(root.get("menus").asText(), new TypeReference<List<ModuleMenu>>() {});
//
//            } catch (IOException e) {
//                log.error("Unable to load module properties file: {}", name, e);
//                throw new RuntimeException("Unable to load module properties file: " + name, e);
//            }
//        }
//    }
}
