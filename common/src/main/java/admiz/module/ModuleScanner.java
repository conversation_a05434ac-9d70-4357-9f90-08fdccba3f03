package admiz.module;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;

import java.lang.reflect.Constructor;
import java.lang.reflect.Modifier;
import java.util.*;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j @RequiredArgsConstructor
public class ModuleScanner {
    final static ObjectMapper mapper = new ObjectMapper(new YAMLFactory());
    private static final ReentrantLock lock = new ReentrantLock();

    static volatile Map<String, Module> moduleMap = null;
    static volatile List<Module> modules = new ArrayList<>();

    final List<String> basePackages;

    public Module get(String id) {
        init();
        return moduleMap.get(id);
    }

    public List<Module> getModules() {
        init();
        return modules;
    }

    private void init() {
        if (moduleMap == null) {
            lock.lock();
            long start = System.currentTimeMillis();
            try {
                if (moduleMap == null) {
                    moduleMap = new HashMap<>();

                    for (String basePackage : basePackages) {
                        // 假设这里有扫描逻辑
                        Set<Class<?>> classSet = scanClassesInPackage(basePackage);

                        for (Class<?> type : classSet) {
                            try {
                                if(Modifier.isAbstract(type.getModifiers())) {
                                    continue;
                                }
                                
                                // 修复 varargs 警告：使用空的 Class 数组
                                Constructor<?> constructor = type.getDeclaredConstructor();
                                Module tmpModule = (Module) constructor.newInstance();

                                String resourceName = "module." + tmpModule.id() + ".yaml";

                                Module module = mapper.readValue(new ClassPathResource(resourceName).getInputStream(), (Class<Module>) type);

                                moduleMap.put(module.id(), module);
                                log.info("module initialized: {}", module);
                            } catch (Exception e) {
                                log.error("failed to load module: {}", type.getName(), e);
                            }
                        }
                    }

                    modules = moduleMap.values()
                            .stream()
                            .sorted(Comparator.comparingInt(Module::order))
                            .toList();
                }
            } finally {
                lock.unlock();
                long cost = System.currentTimeMillis() - start;
                log.info("Found {} modules in {} packages, loaded in {} ms", modules.size(), basePackages, cost);
            }
        }
    }

    private Set<Class<?>> scanClassesInPackage(String basePackage) {
        // 这里应该实现包扫描逻辑
        // 目前返回空集合作为示例
        return new HashSet<>();
    }
}