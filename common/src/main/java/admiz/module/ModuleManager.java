package admiz.module;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.core.io.ClassPathResource;

import java.lang.reflect.Constructor;
import java.lang.reflect.Modifier;
import java.util.*;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j @RequiredArgsConstructor
public class ModuleManager {
    final static ObjectMapper mapper = new ObjectMapper(new YAMLFactory());
    private static final ReentrantLock lock = new ReentrantLock();

    static volatile Map<String, Module> moduleMap = null;
    static volatile List<Module> modules = new ArrayList<>();

    final List<String> moduleClasses;
    final String contextPath;

    public Module get(String id) {
        init();
        return moduleMap.get(id);
    }

    public List<Module> getModules() {
        init();
        return modules;
    }

    private void init() {
        if (moduleMap == null) { // 第一次检查
            lock.lock();
            long start = System.currentTimeMillis();
            try {
                if (moduleMap == null) { // 第二次检查
                    moduleMap = new HashMap<>();

                    for (String moduleClassName : moduleClasses) {
                        try {
                            Class<?> moduleClass = Class.forName(moduleClassName);
                            if(Modifier.isAbstract(moduleClass.getModifiers())) {
                                continue;
                            }

                            // 修复 varargs 警告：使用空的 Class 数组
                            Constructor<?> constructor = moduleClass.getDeclaredConstructor();
                            Module tmpModule = (Module) constructor.newInstance();

                            String resourceName = "module." + moduleClass.getSimpleName() + ".yaml";

                            Module module = mapper.readValue(new ClassPathResource(resourceName).getInputStream(), (Class<Module>) moduleClass);

                            if(!Strings.isBlank(this.contextPath) && !"/".equals(this.contextPath)) {
                                prependContextPath(contextPath, module.menus());
                            }
                            moduleMap.put(module.id(), module);
                            log.info("module initialized: {}", module);
                        } catch (Exception e) {
                            log.error("failed to load module: {}", moduleClassName, e);
                        }
                    }
                }

                modules = moduleMap.values()
                        .stream()
                        .sorted(Comparator.comparingInt(Module::order))
                        .toList();
            } finally {
                lock.unlock();
                long cost = System.currentTimeMillis() - start;
                log.info("Found {} modules from {} classes, loaded in {} ms", modules.size(), moduleClasses, cost);
            }
        }
    }

    private void prependContextPath(String contextPath, List<ModuleMenu> menus) {
        for (ModuleMenu menu : menus) {
            String url = menu.getUrl();
            if (menu.getType() != ModuleMenu.MenuType.FOLDER && !url.startsWith("http://") && !url.startsWith("https://")) {
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append(contextPath);
                while (contextPath.endsWith("/")) {
                    stringBuilder.deleteCharAt(contextPath.length() - 1);
                }

                stringBuilder.append("/");

                while (url.startsWith("/")) {
                    url = url.substring(1);
                }

                stringBuilder.append(url);

                menu.setUrl(stringBuilder.toString());
            }

            if (menu.getMenus() != null) {
                prependContextPath(contextPath, menu.getMenus());
            }
        }
    }
}