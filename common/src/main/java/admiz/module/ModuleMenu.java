package admiz.module;

import lombok.Data;
import org.apache.logging.log4j.util.Strings;

import java.util.ArrayList;
import java.util.List;

@Data
public class ModuleMenu {
    public enum MenuType {
        FOLDER,
        MENU
    }
    private String moduleKey;
    private String id;
    private String parentId;
    private String title;
    private String url;
    private boolean enabled = true;
    private MenuType type;
    private String icon = "ico-menu";
    private String description = "";

    private List<ModuleMenu> menus = new ArrayList<>();

    public MenuType getType() {
        return type == null && Strings.isBlank(url) ? MenuType.FOLDER : MenuType.MENU;
    }
}
