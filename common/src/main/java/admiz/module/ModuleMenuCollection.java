package admiz.module;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;

@Data @EqualsAndHashCode(callSuper = true)
public class ModuleMenuCollection extends ArrayList<ModuleMenu> {
    private String moduleKey;
    private String id;
    private String parentId;
    private String title;
    private String url;
    private boolean enabled = true;
    private ModuleMenu.MenuType type = ModuleMenu.MenuType.MENU;
    private String icon = "ico-menu";
    private String description = "";
}
