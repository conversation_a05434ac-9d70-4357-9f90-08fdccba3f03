package admiz.system.model;

import org.babyfish.jimmer.sql.*;

@Entity
@Table(name = "system_dept_user")
public interface SystemDeptUser {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    long id();

    @IdView
    long userId();

    @IdView
    long deptId();

    @ManyToOne
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    SystemUser user();

    @ManyToOne
    @JoinColumn(name = "dept_id", referencedColumnName = "id")
    SystemDept dept();
}
