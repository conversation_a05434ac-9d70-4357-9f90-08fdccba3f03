package admiz.system.model;

import admiz.common.entity.BaseEntity;
import admiz.common.entity.OpAuditedEntity;
import org.babyfish.jimmer.sql.*;
import org.jetbrains.annotations.Nullable;

import java.util.List;

@Entity
@Table(name = "system_dept")
public interface SystemDept extends BaseEntity, OpAuditedEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    long id();

    long parentId();

    String ancestors(); // 祖级列表，用于层级查询

    String name();

    int sortNum();

    long leaderId();

    String phone();

    String email();

    boolean disabled();

    boolean deleted();

    @OneToMany(mappedBy = "dept")
    List<SystemDeptUser> deptUsers();
}
