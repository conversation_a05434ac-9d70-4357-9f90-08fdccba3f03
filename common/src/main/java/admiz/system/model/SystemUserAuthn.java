package admiz.system.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.Accessors;
import org.babyfish.jimmer.sql.*;

import java.time.LocalDateTime;
import java.util.Arrays;

@Entity
@Table(name = "system_user_authn")
public interface SystemUserAuthn {
    @Getter
    @Accessors(fluent = true)
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum AuthnType {
        PASSWORD("PASSWORD", "帐号密码"),
        MOBILE_PHONE("MOBILE_PHONE", "手机验证码"),
        WECHAT_MP("WECHAT_MP", "微信公众号"),
        WECHAT_MINI_APP("WECHAT_MINI_APP", "微信小程序"),
        WECHAT_UNION_ID("WECHAT_UNION_ID", "微信 UnionID"),
        ALIPAY_MP("ALIPAY_MP", "支付宝服务号"),
        ;

        private final String value;
        private final String displayName;

        @JsonCreator
        public static AuthnType fromString(String strValue) {
            return Arrays.stream(AuthnType.values()).filter(value -> value.value().equals(strValue)).findFirst().orElseThrow();
        }
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    long id();

    long userId();

    @Key(group = "authn_unique_key")
    String authnId();
    @Key(group = "authn_unique_key")
    AuthnType authnType();

    String authnData();

    boolean disabled();

    LocalDateTime createTime();
    LocalDateTime updateTime();


    // @ManyToOne
    // @JoinColumn(name = "subject")
    // SystemUser user();
}
