package admiz.system.model;

import admiz.common.DataRealm;
import org.babyfish.jimmer.sql.*;

import java.util.List;
import java.util.Map;

@Entity
@Table(name = "system_dict")
public interface SystemDict {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    long id();

    @Key
    String dictName();

    DataRealm dictType();

    int sortNum();

    @Serialized
    Map<String, String> dictEntries();

    @Serialized
    List<String> sortedKeys();
}
