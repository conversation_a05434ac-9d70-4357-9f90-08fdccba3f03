//package admiz.system.model;
//
//import admiz.common.entity.UlIdGenerator;
//import com.fasterxml.jackson.annotation.JsonCreator;
//import lombok.AccessLevel;
//import lombok.AllArgsConstructor;
//import lombok.Getter;
//import lombok.experimental.Accessors;
//import org.babyfish.jimmer.sql.*;
//import org.jetbrains.annotations.Nullable;
//
//import java.time.LocalDateTime;
//import java.util.Arrays;
//
//@Entity
//@Table(name = "system_user_op_logs")
//public interface SystemUserOpLog {
//   @Getter
//   @Accessors(fluent = true)
//   @AllArgsConstructor(access = AccessLevel.PRIVATE)
//   enum OpKind {
//       ADMIN_LOGIN("后台用户登录"),
//       USER_LOGIN("前台用户登录"),
//       UPDATE_PASSWORD("更新用户"),
//       RUNAS_USER("模拟用户身份"),
//       LOCK_USER("封禁用户"),
//       UNLOCK_USER("解禁用户"),
//       ASSIGN_ROLE("给用户授与角色"),
//       ASSIGN_PERMISSION("给用户授权"),
//       CREATE("创建"),
//       UPDATE("更新"),
//       DELETE("删除"),
//       UNKNOWN("未知操作"),
//       ;
//
//       private final String displayName;
//
//       @JsonCreator
//       public static OpKind fromString(String strValue) {
//           return Arrays.stream(OpKind.values()).filter(value -> value.name().equals(strValue)).findFirst().orElse(UNKNOWN);
//       }
//   }
//
//   @Getter
//   @Accessors(fluent = true)
//   @AllArgsConstructor(access = AccessLevel.PRIVATE)
//   enum OpSubjectKind {
//       ADMIN_USER("后台用户"),
//       USER("前台用户"),
//       ROLE("角色"),
//       RESOURCE("资源"),
//       MERCHANT("商户"),
//       OTHER("其他"),
//       ;
//
//       private final String displayName;
//
//       @JsonCreator
//       public static OpSubjectKind fromString(String strValue) {
//           return Arrays.stream(OpSubjectKind.values()).filter(value -> value.name().equals(strValue)).findFirst().orElse(OTHER);
//       }
//   }
//
//   @Id
//   @GeneratedValue(strategy = GenerationType.USER, generatorType = UlIdGenerator.class)
//   String id();
//
//   long userId();
//   String subjectKind();
//   String subjectId();
//   OpKind opKind();
//   String info();
//   String data();
//   @Nullable
//   String clientIp();
//   LocalDateTime createTime();
//}
//
