// package admiz.system.model;
//
// import com.fasterxml.jackson.annotation.JsonCreator;
// import lombok.AccessLevel;
// import lombok.AllArgsConstructor;
// import lombok.Getter;
// import lombok.experimental.Accessors;
// import org.babyfish.jimmer.sql.*;
//
// import java.time.LocalDateTime;
// import java.util.Arrays;
//
// @Entity
// @Table(name = "system_full_text_search")
// public interface SystemFullTextSearch {
//    @Getter
//    @Accessors(fluent = true)
//    @AllArgsConstructor(access = AccessLevel.PRIVATE)
//    enum EntityKind {
//        USER("用户表"),
//        DEPARTMENT("部门表"),
//        RESOURCE("资源表"),
//        ;
//
//        private final String displayName;
//
//        @JsonCreator
//        public static EntityKind of(String strValue) {
//            return Arrays.stream(EntityKind.values()).filter(value -> value.name().equals(strValue)).findFirst().orElseThrow();
//        }
//    }
//
//    @Id
//    @GeneratedValue(strategy = GenerationType.IDENTITY)
//    long id();
//
//    @Key
//    long entityLongId();
//    @Key
//    String entityStringId();
//    EntityKind entityKind();
//    String searchText();
//    LocalDateTime updateTime();
// }
