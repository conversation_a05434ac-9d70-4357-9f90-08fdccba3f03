package admiz.system.model;

import admiz.common.entity.BaseEntity;
import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.Accessors;
import org.babyfish.jimmer.sql.*;
import org.jetbrains.annotations.Nullable;

import java.util.Arrays;

/**
 * 系统权限操作日志实体
 * 记录所有权限相关的操作，包括用户权限变更、角色权限分配等
 *
 * 设计说明：
 * - OperationType: 纯粹的业务操作类型
 * - OperationResult: 业务层面的操作结果
 * - RecordStatus: 技术层面的日志记录状态
 **/
@Entity
@Table(name = "system_permission_log")
public interface SystemPermissionLog extends BaseEntity {

    // 记录状态
    @Column(name = "record_status")
    RecordStatus recordStatus();

    @Getter
    @Accessors(fluent = true)
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum OperationType {
        // 用户认证操作
        USER_LOGIN("用户登录"),
        USER_LOGOUT("用户登出"),

        // 权限检查操作
        PERMISSION_CHECK("权限检查"),

        // 用户管理操作
        USER_ROLE_ASSIGN("用户角色分配"),
        USER_ROLE_REMOVE("用户角色移除"),
        USER_UPDATE("用户信息更新"),
        USER_DISABLE("用户禁用"),
        USER_ENABLE("用户启用"),
        USER_DELETE("用户删除"),

        // 角色管理操作
        ROLE_CREATE("角色创建"),
        ROLE_UPDATE("角色更新"),
        ROLE_DELETE("角色删除"),
        ROLE_PERMISSION_ASSIGN("角色权限分配"),
        ROLE_PERMISSION_REMOVE("角色权限移除"),

        // 资源管理操作
        RESOURCE_CREATE("资源创建"),
        RESOURCE_UPDATE("资源更新"),
        RESOURCE_DELETE("资源删除"),

        // 数据权限操作
        DATA_SCOPE_APPLIED("数据权限应用"),

        // 系统操作
        PERMISSION_INIT("权限初始化"),
        PERMISSION_SYNC("权限同步");

        private final String displayName;

        @JsonCreator
        public static OperationType fromString(String strValue) {
            return Arrays.stream(OperationType.values()).filter(value -> value.name().equals(strValue)).findFirst().orElseThrow();
        }
    }

    @Getter
    @Accessors(fluent = true)
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum OperationResult {
        SUCCESS("成功"),        // 操作成功
        DENIED("拒绝"),         // 权限被拒绝
        FAILED("失败"),         // 操作失败
        PARTIAL("部分成功");     // 部分成功

        private final String displayName;

        @JsonCreator
        public static OperationResult fromString(String strValue) {
            return Arrays.stream(OperationResult.values()).filter(value -> value.name().equals(strValue)).findFirst().orElseThrow();
        }
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    long id();

    // 操作类型
    OperationType operationType();

    // 操作结果
    OperationResult operationResult();

    @Getter
    @Accessors(fluent = true)
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum RecordStatus {
        RECORDED("已记录"),      // 成功记录到日志
        RECORD_FAILED("记录失败"); // 日志记录失败

        private final String displayName;

        @JsonCreator
        public static RecordStatus fromString(String strValue) {
            return Arrays.stream(RecordStatus.values()).filter(value -> value.name().equals(strValue)).findFirst().orElseThrow();
        }
    }

    // 操作用户ID
    @Nullable Long operatorUserId();

    // 操作用户登录名
    @Nullable String operatorUserLogin();

    // 目标用户ID（如果操作涉及其他用户）
    @Nullable Long targetUserId();

    // 目标用户登录名
    @Nullable String targetUserLogin();

    // 目标角色ID（如果操作涉及角色）
    @Nullable Long targetRoleId();

    // 目标角色名称
    @Nullable String targetRoleName();

    // 目标资源ID（如果操作涉及资源）
    @Nullable Long targetResourceId();

    // 目标权限编码
    @Nullable String targetPermission();

    // 操作描述
    String operationDescription();

    // 操作详情（JSON格式）
    @Nullable String operationDetails();

    // 客户端IP地址
    @Nullable String clientIp();

    // 用户代理信息
    @Nullable String userAgent();

    // 会话ID
    @Nullable String sessionId();

    // 操作耗时（毫秒）
    @Nullable Long operationDuration();

    // 错误信息（如果操作失败）
    @Nullable String errorMessage();

    // 备注
    @Nullable String remark();
}
