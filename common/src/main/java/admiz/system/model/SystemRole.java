package admiz.system.model;

import admiz.common.entity.BaseEntity;
import admiz.common.entity.OpAuditedEntity;
import org.babyfish.jimmer.sql.*;

import java.util.List;

@Entity
@Table(name = "system_role")
public interface SystemRole  extends BaseEntity, OpAuditedEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    long id();

    @Key
    String name();
    String displayName();
    int sortNum();
    boolean disabled();
    boolean deleted();
    String remark();


    @ManyToMany
    @JoinTable(
            name = "system_role_resource",
            joinColumnName = "role_id",
            inverseJoinColumnName = "resource_id"
    )
    List<SystemResource> resources();
}
