package admiz.system.model;

import admiz.common.DataRealm;
import admiz.common.entity.BaseEntity;
import org.babyfish.jimmer.sql.*;

@Entity
@Table(name = "system_config")
public interface SystemConfig  extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    long id();

    @Key
    String name();
    String displayName();
    DataRealm configType();
    String configValue();
    String remark();
}
