package admiz.system.model;

import admiz.common.entity.BaseEntity;
import admiz.common.entity.OpAuditedEntity;
import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.Accessors;
import org.babyfish.jimmer.sql.*;
import org.jetbrains.annotations.Nullable;

import java.util.Arrays;
import java.util.List;

@Entity
@Table(name = "system_resource")
public interface SystemResource extends BaseEntity, OpAuditedEntity {
    @Getter
    @Accessors(fluent = true)
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum ResourceType {
        FOLDER("目录"),
        MENU("菜单"),
        FUNCTION("功能"),
        DATA("数据权限"),
        ;

        private final String displayName;

        @JsonCreator
        public static ResourceType fromString(String strValue) {
            return Arrays.stream(ResourceType.values()).filter(value -> value.name().equals(strValue)).findFirst().orElseThrow();
        }
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    long id();

    String name();

    String displayName();

    @Nullable
    Long parentId();

    int sortNum();

    @Key
    String permission();

    String url();

    String target();

    ResourceType resourceType();

    boolean disabled();

    boolean hidden();

    boolean autoRefresh();

    String icon();

    String remark();

    @ManyToMany(mappedBy = "resources")
    List<SystemRole> roles();
}
