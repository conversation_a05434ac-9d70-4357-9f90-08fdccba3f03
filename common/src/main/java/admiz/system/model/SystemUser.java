package admiz.system.model;

import admiz.common.DataRealm;
import admiz.common.entity.BaseEntity;
import admiz.common.entity.OpAuditedEntity;
import org.babyfish.jimmer.Formula;
import org.babyfish.jimmer.sql.*;
import org.jetbrains.annotations.Nullable;

import java.time.LocalDateTime;
import java.util.List;

@Entity @Table(name = "system_user")
public interface SystemUser extends BaseEntity, OpAuditedEntity {
    public static final String BUILTIN_ADMIN_USER = "admin";

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    long id();

    @Key
    String login();

    String displayName();

    DataRealm realm();

    String email();

    String mobile();

    String sex();

    String avatar();

//    boolean disabled();

    int failureLoginAttempts();

    boolean locked();

    @Nullable
    LocalDateTime lockedUntil();

    @Nullable
    String lockReason();

    LocalDateTime pwdUpdateDate();

    String remark();

    @ManyToMany
    @JoinTable(
            name = "system_user_role",
            joinColumnName = "user_id",
            inverseJoinColumnName = "role_id"
    )
    List<SystemRole> roles();

    @OneToMany(mappedBy = "user")
    List<SystemDeptUser> deptUsers();

    // @OneToMany(mappedBy = "user")
    // List<SystemUserAuthn> authnSources();

    @Formula(dependencies = {"login"})
    default boolean isBuiltinAdmin() {
        return BUILTIN_ADMIN_USER.equalsIgnoreCase(this.login());
    }

    @Formula(dependencies = {"login", "id"})
    default String toStringInfo() {
        return "User[" + login() + "(" + id() + ")]";
    }

    // TODO: 添加获取主要部门的便捷方法，等Jimmer生成完成后实现

}
