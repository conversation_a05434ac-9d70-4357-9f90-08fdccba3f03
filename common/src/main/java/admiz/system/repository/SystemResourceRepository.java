package admiz.system.repository;

import admiz.system.model.*;
import com.google.common.base.Strings;
import org.babyfish.jimmer.spring.repository.JRepository;
import org.babyfish.jimmer.spring.repository.SpringOrders;
import org.babyfish.jimmer.spring.repository.support.SpringPageFactory;
import org.babyfish.jimmer.sql.ast.Predicate;
import org.babyfish.jimmer.sql.ast.query.Order;
import org.babyfish.jimmer.sql.ast.table.AssociationTable;
import org.babyfish.jimmer.sql.fetcher.Fetcher;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public interface SystemResourceRepository extends JRepository<SystemResource, Long> {
    SystemResourceTable table = Tables.SYSTEM_RESOURCE_TABLE;

    /**
     * 根据ID列表查询资源
     */
    default List<SystemResource> findByIds(List<Long> ids) {
        return sql().createQuery(table)
                .where(table.id().in(ids))
                .select(table)
                .execute();
    }

    /**
     * 根据角色列表查询资源
     */
    default List<SystemResource> findResourceForRoles(List<SystemRole> roles, @Nullable Fetcher<SystemResource> fetcher) {
        if (roles.isEmpty()) {
            return List.of();
        }
        
        List<Long> roleIds = roles.stream().map(SystemRole::id).toList();
        
        // 使用 AssociationTable 来查询多对多关系
        var assoc = AssociationTable.of(SystemRoleTableEx.class, SystemRoleTableEx::resources);
        return sql().createAssociationQuery(assoc)
                .where(assoc.source().id().in(roleIds))
                .select(assoc.target().fetch(fetcher))
                .execute();
    }

    /**
     * 根据用户ID查询用户拥有的资源权限（无 Fetcher 参数）
     */
    default List<SystemResource> findUserResources(long userId) {
        return findUserResources(userId, null);
    }

    /**
     * 根据用户ID查询用户拥有的资源权限（带 Fetcher 参数）
     */
    default List<SystemResource> findUserResources(long userId, @Nullable Fetcher<SystemResource> fetcher) {
        // 先查询用户的角色
        var userRoleAssoc = AssociationTable.of(SystemUserTableEx.class, SystemUserTableEx::roles);
        List<SystemRole> userRoles = sql().createAssociationQuery(userRoleAssoc)
                .where(userRoleAssoc.source().id().eq(userId))
                .select(userRoleAssoc.target())
                .execute();

        if (userRoles.isEmpty()) {
            return List.of();
        }

        // 查询这些角色的资源
        List<Long> roleIds = userRoles.stream().map(SystemRole::id).toList();
        var roleResourceAssoc = AssociationTable.of(SystemRoleTableEx.class, SystemRoleTableEx::resources);
        
        return sql().createAssociationQuery(roleResourceAssoc)
                .where(roleResourceAssoc.source().id().in(roleIds))
                .select(roleResourceAssoc.target().fetch(fetcher))
                .execute()
                .stream()
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 根据权限编码查询资源
     */
    default List<SystemResource> findByPermission(String permission) {
        return sql().createQuery(table)
                .where(table.permission().eq(permission))
                .select(table)
                .execute();
    }



    /**
     * 根据资源名称查询资源
     */
    default List<SystemResource> findByName(String name) {
        return sql().createQuery(table)
                .where(table.name().eq(name))
                .select(table)
                .execute();
    }

    /**
     * 查询菜单类型的资源
     */
    default List<SystemResource> findMenuResources(@Nullable Fetcher<SystemResource> fetcher) {
        return sql().createQuery(table)
                .where(table.resourceType().in(
                    Arrays.asList(
                        SystemResource.ResourceType.MENU,
                        SystemResource.ResourceType.FOLDER
                    )
                ))
                .where(table.disabled().eq(false))
                .orderBy(table.sortNum().asc())
                .select(table.fetch(fetcher))
                .execute();
    }

    /**
     * 根据用户ID查询用户拥有的资源权限（别名方法，兼容旧代码）
     */
    default List<SystemResource> findResourcesByUserId(long userId, @Nullable Fetcher<SystemResource> fetcher) {
        return findUserResources(userId, fetcher);
    }

    /**
     * 根据角色ID查询资源
     */
    default List<SystemResource> findResourcesByRoleId(long roleId, @Nullable Fetcher<SystemResource> fetcher) {
        var assoc = AssociationTable.of(SystemRoleTableEx.class, SystemRoleTableEx::resources);
        return sql().createAssociationQuery(assoc)
                .where(assoc.source().id().eq(roleId))
                .select(assoc.target().fetch(fetcher))
                .execute();
    }

    /**
     * 根据资源类型查询资源
     */
    default List<SystemResource> findByResourceType(SystemResource.ResourceType resourceType, 
                                                   @Nullable Fetcher<SystemResource> fetcher) {
        return sql().createQuery(table)
                .where(table.resourceType().eq(resourceType))
                .where(table.disabled().eq(false))
                .orderBy(table.sortNum().asc())
                .select(table.fetch(fetcher))
                .execute();
    }

    /**
     * 查询所有可用的资源
     */
    default List<SystemResource> findAllAvailable(@Nullable Fetcher<SystemResource> fetcher) {
        return sql().createQuery(table)
                .where(table.disabled().eq(false))
                .orderBy(table.sortNum().asc())
                .select(table.fetch(fetcher))
                .execute();
    }

    /**
     * 根据父级ID查询子资源
     */
    default List<SystemResource> findByParentId(@Nullable Long parentId, @Nullable Fetcher<SystemResource> fetcher) {
        return sql().createQuery(table)
                .where(table.parentId().eq(parentId))
                .where(table.disabled().eq(false))
                .orderBy(table.sortNum().asc())
                .select(table.fetch(fetcher))
                .execute();
    }

    /**
     * 构建资源树结构
     */
    default List<SystemResource> findResourceTree(@Nullable Fetcher<SystemResource> fetcher) {
        return sql().createQuery(table)
                .where(table.disabled().eq(false))
                .orderBy(table.sortNum().asc())
                .select(table.fetch(fetcher))
                .execute();
    }

    /**
     * 根据用户ID查询菜单类型的资源
     */
    default List<SystemResource> findUserMenuResources(long userId, @Nullable Fetcher<SystemResource> fetcher) {
        List<SystemResource> userResources = findUserResources(userId, fetcher);
        
        return userResources.stream()
                .filter(resource -> resource.resourceType() == SystemResource.ResourceType.MENU || 
                                   resource.resourceType() == SystemResource.ResourceType.FOLDER)
                .filter(resource -> !resource.disabled())
                .sorted((r1, r2) -> Integer.compare(r1.sortNum(), r2.sortNum()))
                .collect(Collectors.toList());
    }

    /**
     * 根据用户ID和权限编码查询资源
     */
    default List<SystemResource> findUserResourcesByPermission(long userId, String permission, @Nullable Fetcher<SystemResource> fetcher) {
        List<SystemResource> userResources = findUserResources(userId, fetcher);
        
        return userResources.stream()
                .filter(resource -> permission.equals(resource.permission()))
                .collect(Collectors.toList());
    }

    /**
     * 检查用户是否拥有指定权限
     */
    default boolean hasPermission(long userId, String permission) {
        return !findUserResourcesByPermission(userId, permission, null).isEmpty();
    }

    /**
     * 分页查询资源列表
     */
    default Page<SystemResource> paginate(
            @Nullable String searchText,
            @Nullable SystemResource.ResourceType resourceType,
            @Nullable Boolean disabled,
            Pageable pageable,
            @Nullable Fetcher<SystemResource> fetcher
    ) {
        Order[] sortFields = SpringOrders.toOrders(table, pageable.getSort());

        var query = sql().createQuery(table);

        if (!Strings.isNullOrEmpty(searchText)) {
            String searchPattern = "%" + searchText + "%";
            query = query.where(
                Predicate.or(
                    table.name().ilike(searchPattern),
                    table.displayName().ilike(searchPattern),
                    table.permission().ilike(searchPattern),
                    table.remark().ilike(searchPattern)
                )
            );
        }

        if (resourceType != null) {
            query = query.where(table.resourceType().eq(resourceType));
        }

        if (disabled != null) {
            query = query.where(table.disabled().eq(disabled));
        }

        return query
                .orderBy(sortFields)
                .select(table.fetch(fetcher))
                .fetchPage(pageable.getPageNumber(), pageable.getPageSize(), SpringPageFactory.getInstance());
    }

    default boolean isBuiltinResource(Long resourceId){
        return resourceId <= 1000;
    }
}