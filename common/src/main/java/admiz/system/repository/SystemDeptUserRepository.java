package admiz.system.repository;

import admiz.system.model.SystemDeptUser;
import admiz.system.model.SystemDeptUserTable;
import admiz.system.model.Tables;
import org.babyfish.jimmer.spring.repository.JRepository;
import org.babyfish.jimmer.sql.fetcher.Fetcher;
import org.jetbrains.annotations.Nullable;

import java.util.List;

public interface SystemDeptUserRepository extends JRepository<SystemDeptUser, Long> {
    SystemDeptUserTable table = Tables.SYSTEM_DEPT_USER_TABLE;

    default List<SystemDeptUser> findByUserId(Long userId, @Nullable Fetcher<SystemDeptUser> fetcher) {
        return sql().createQuery(table)
                .where(table.userId().eq(userId))
                .select(table.fetch(fetcher))
                .execute();
    }

    default List<SystemDeptUser> findByDeptId(Long deptId, @Nullable Fetcher<SystemDeptUser> fetcher) {
        return sql().createQuery(table)
                .where(table.deptId().eq(deptId))
                .select(table.fetch(fetcher))
                .execute();
    }

    default boolean existsByUserIdAndDeptId(Long userId, Long deptId) {
        return sql().createQuery(table)
                .where(table.userId().eq(userId))
                .where(table.deptId().eq(deptId))
                .exists();
    }

    default int deleteByUserId(Long userId) {
        return sql().createDelete(table)
                .where(table.userId().eq(userId))
                .execute();
    }

    default int deleteByDeptId(Long deptId) {
        return sql().createDelete(table)
                .where(table.deptId().eq(deptId))
                .execute();
    }

    default int deleteByUserIdAndDeptId(Long userId, Long deptId) {
        return sql().createDelete(table)
                .where(table.userId().eq(userId))
                .where(table.deptId().eq(deptId))
                .execute();
    }
}
