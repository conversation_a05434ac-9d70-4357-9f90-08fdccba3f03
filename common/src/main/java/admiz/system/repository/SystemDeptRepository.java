package admiz.system.repository;

import admiz.system.model.SystemDept;
import admiz.system.model.SystemDeptTable;
import admiz.system.model.Tables;
import com.google.common.base.Strings;
import org.babyfish.jimmer.spring.repository.JRepository;
import org.babyfish.jimmer.spring.repository.SpringOrders;
import org.babyfish.jimmer.spring.repository.support.SpringPageFactory;
import org.babyfish.jimmer.sql.ast.Predicate;
import org.babyfish.jimmer.sql.ast.query.Order;
import org.babyfish.jimmer.sql.fetcher.Fetcher;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

public interface SystemDeptRepository extends JRepository<SystemDept, Long> {
    SystemDeptTable table = Tables.SYSTEM_DEPT_TABLE;

    default Page<SystemDept> paginate(@Nullable String searchText,
                                      @Nullable Boolean disabled,
                                      @NotNull Pageable pageable,
                                      @Nullable Fetcher<SystemDept> fetcher) {
        Order[] sortFields = SpringOrders.toOrders(table, pageable.getSort());

        var query = sql().createQuery(table);

        if (!Strings.isNullOrEmpty(searchText)) {
            String searchPattern = "%" + searchText + "%";
            query = query.where(
                    Predicate.or(
                            table.name().ilike(searchPattern),
                            table.phone().ilike(searchPattern),
                            table.email().ilike(searchPattern)
                    )
            );
        }

        if (disabled != null) {
            query = query.where(table.disabled().eq(disabled));
        }

        return query.where(table.deleted().eq(false))
                .orderBy(sortFields)
                .select(table.fetch(fetcher))
                .fetchPage(pageable.getPageNumber(), pageable.getPageSize(), SpringPageFactory.getInstance());
    }

    default List<SystemDept> findAllAvailable(@Nullable Fetcher<SystemDept> fetcher) {
        return sql().createQuery(table)
                .where(table.disabled().eq(false))
                .where(table.deleted().eq(false))
                .orderBy(table.sortNum().asc())
                .select(table.fetch(fetcher))
                .execute();
    }

    default List<SystemDept> findDeptTree(@Nullable Fetcher<SystemDept> fetcher) {
        return sql().createQuery(table)
                .where(table.deleted().eq(false))
                .orderBy(table.parentId().asc(), table.sortNum().asc())
                .select(table.fetch(fetcher))
                .execute();
    }

    default List<SystemDept> findByParentId(Long parentId, @Nullable Fetcher<SystemDept> fetcher) {
        return sql().createQuery(table)
                .where(table.parentId().eq(parentId))
                .where(table.deleted().eq(false))
                .orderBy(table.sortNum().asc())
                .select(table.fetch(fetcher))
                .execute();
    }

    default Optional<SystemDept> findByName(String name, @Nullable Fetcher<SystemDept> fetcher) {
        return sql().createQuery(table)
                .where(table.name().eq(name))
                .where(table.deleted().eq(false))
                .select(table.fetch(fetcher))
                .execute()
                .stream()
                .findFirst();
    }

    default boolean existsByName(@NotNull String name) {
        return sql().createQuery(table)
                .where(table.name().eq(name))
                .where(table.deleted().eq(false))
                .exists();
    }

    default boolean existsByNameAndIdNot(@NotNull String name, @Nullable Long excludeId) {
        var query = sql().createQuery(table)
                .where(table.name().eq(name))
                .where(table.deleted().eq(false));

        if (excludeId != null) {
            query = query.where(table.id().ne(excludeId));
        }

        return query.exists();
    }
}
