package admiz.system.repository;

import admiz.system.model.*;
import com.google.common.base.Strings;
import org.babyfish.jimmer.spring.repository.JRepository;
import org.babyfish.jimmer.spring.repository.SpringOrders;
import org.babyfish.jimmer.spring.repository.support.SpringPageFactory;
import org.babyfish.jimmer.sql.ast.Predicate;
import org.babyfish.jimmer.sql.ast.mutation.SimpleSaveResult;
import org.babyfish.jimmer.sql.ast.query.Order;
import org.babyfish.jimmer.sql.ast.table.AssociationTable;
import org.babyfish.jimmer.sql.fetcher.Fetcher;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

public interface SystemUserRepository extends JRepository<SystemUser, Long> {
    SystemUserTable table = Tables.SYSTEM_USER_TABLE;

    Optional<SystemUser> findByLogin(String loginName, @Nullable Fetcher<SystemUser> fetcher);
    List<SystemUser> findByLoginStartsWith(String loginName, @Nullable Fetcher<SystemUser> fetcher);

    default boolean lock(SystemUser user, LocalDateTime lockedUntil) {
        SystemUser updatedAuthn = Immutables.createSystemUser(draft -> {
            draft.setId(user.id());
            draft.setLocked(true);
            draft.setLockedUntil(lockedUntil);
        });

        SimpleSaveResult<SystemUser> result = sql().saveCommand(updatedAuthn)
                .execute();

        return result.getTotalAffectedRowCount()  == 1;
    }

    default boolean lock(SystemUser user, Duration duration) {
        return lock(user, LocalDateTime.now().plus(duration));
    }

    default boolean unlock(SystemUser user) {
        SystemUser updatedAuthn = Immutables.createSystemUser(draft -> {
            draft.setId(user.id());
            draft.setLocked(false);
            draft.setLockedUntil(null);
        });

        SimpleSaveResult<SystemUser> result = sql().saveCommand(updatedAuthn)
                .execute();

        return result.getTotalAffectedRowCount() == 1;
    }

    default Page<SystemUser> paginate(
            @Nullable String name,
            @Nullable String locked,
            Pageable pageable,
            @Nullable Fetcher<SystemUser> fetcher
    ) {
        // Order[] sortFields = pageable.getSort().isSorted() ? SpringOrders.toOrders(table, pageable.getSort()) : null;
        Order[] sortFields = SpringOrders.toOrders(table, pageable.getSort());
        return sql().createQuery(table)
                .where(table.login().ilikeIf(name))
                .where(table.locked().eqIf(!Strings.isNullOrEmpty(locked), Boolean.valueOf(locked)))
                .orderBy(sortFields)
                .select(table.fetch(fetcher))
                .fetchPage(pageable.getPageNumber(), pageable.getPageSize(), SpringPageFactory.getInstance());
    }

    default List<SystemRole> getUserRoles(long userId) {
        var assoc = AssociationTable.of(SystemUserTableEx.class, SystemUserTableEx::roles);
        return sql().createAssociationQuery(assoc)
                .where(assoc.source().id().eq(userId))
                .select(assoc.target())
                .execute();
    }

    default void assignRoles(long userId, List<Long> roleIds) {
        SystemUser user = Immutables.createSystemUser(draft -> {
            draft.setId(userId);
            List<SystemRole> roles = roleIds.stream()
                    .map(roleId -> Immutables.createSystemRole(roleDraft ->
                            roleDraft.setId(roleId)))
                    .toList();
            draft.setRoles(roles);
        });
        save(user);
    }

    default void removeRoles(long userId, List<Long> roleIds) {
        List<SystemRole> currentRoles = getUserRoles(userId);
        List<SystemRole> remainingRoles = currentRoles.stream()
                .filter(role -> !roleIds.contains(role.id()))
                .toList();

        SystemUser user = Immutables.createSystemUser(draft -> {
            draft.setId(userId);
            draft.setRoles(remainingRoles);
        });
        save(user);
    }

    default void removeAllRoles(long userId) {
        SystemUser user = Immutables.createSystemUser(draft -> {
            draft.setId(userId);
            draft.setRoles(List.of());
        });
        save(user);
    }

    default boolean hasRole(long userId, String roleName) {
        return getUserRoles(userId).stream()
                .anyMatch(role -> roleName.equals(role.name()));
    }

    default boolean hasAnyRole(long userId, List<String> roleNames) {
        List<SystemRole> userRoles = getUserRoles(userId);
        return userRoles.stream()
                .anyMatch(role -> roleNames.contains(role.name()));
    }
}

