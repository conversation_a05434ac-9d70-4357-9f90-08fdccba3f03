package admiz.system.repository;

import admiz.system.model.*;
import com.google.common.base.Strings;
import org.babyfish.jimmer.spring.repository.JRepository;
import org.babyfish.jimmer.spring.repository.SpringOrders;
import org.babyfish.jimmer.spring.repository.support.SpringPageFactory;
import org.babyfish.jimmer.sql.ast.mutation.SimpleSaveResult;
import org.babyfish.jimmer.sql.ast.query.Order;
import org.babyfish.jimmer.sql.ast.table.AssociationTable;
import org.babyfish.jimmer.sql.fetcher.Fetcher;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.babyfish.jimmer.sql.ast.Predicate;

import java.util.List;
import java.util.Optional;

public interface SystemRoleRepository extends JRepository<SystemRole, Long> {
    SystemRoleTable table = Tables.SYSTEM_ROLE_TABLE;
    Logger log = LoggerFactory.getLogger(SystemRoleRepository.class);

    /**
     * 查询用户的角色列表
     */
    default List<SystemRole> findUserRoles(long userId) {
        var assoc = AssociationTable.of(SystemUserTableEx.class, SystemUserTableEx::roles);
        List<SystemRole> roles = sql().createAssociationQuery(assoc)
                .where(assoc.source().id().eq(userId))
                .select(assoc.target())
                .execute();

        return roles;
    }

    /**
     * 分页查询角色列表
     */
    default Page<SystemRole> paginate(
            @Nullable String name,
            @Nullable Boolean disabled,
            Pageable pageable,
            @Nullable Fetcher<SystemRole> fetcher
    ) {
        log.debug("SystemRoleRepository.paginate 开始 - name: '{}', disabled: {}, pageable: {}", 
                 name, disabled, pageable);
        
        Order[] sortFields = SpringOrders.toOrders(table, pageable.getSort());
        log.debug("排序字段: {}", java.util.Arrays.toString(sortFields));
        
        var query = sql().createQuery(table)
                .where(table.deleted().eq(false));

        if (!Strings.isNullOrEmpty(name)) {
            log.debug("应用搜索条件 - 在 name、displayName、remark 中搜索: '{}'", name);
            String searchPattern = "%" + name + "%";

            query = query.where(
                Predicate.or(
                    table.name().ilike(searchPattern),
                    table.displayName().ilike(searchPattern),
                    table.remark().ilike(searchPattern)
                )
            );
        }
        
        if (disabled != null) {
            log.debug("应用禁用状态查询条件 - disabled = {}", disabled);
            query = query.where(table.disabled().eq(disabled));
        }
        
        log.debug("执行分页查询 - 页码: {}, 每页大小: {}", pageable.getPageNumber(), pageable.getPageSize());
        
        Page<SystemRole> result = query
                .orderBy(sortFields)
                .select(table.fetch(fetcher))
                .fetchPage(pageable.getPageNumber(), pageable.getPageSize(), SpringPageFactory.getInstance());
        
        log.debug("SystemRoleRepository.paginate 结果 - 总数: {}, 当前页数据: {}", 
                 result.getTotalElements(), result.getNumberOfElements());
        
        if (log.isDebugEnabled() && result.hasContent()) {
            log.debug("查询结果详情:");
            result.getContent().forEach(role -> 
                log.debug("  - id: {}, name: '{}', displayName: '{}', remark: '{}', disabled: {}", 
                         role.id(), role.name(), role.displayName(), 
                         role.remark() != null ? role.remark() : "null", role.disabled()));
        }
        
        return result;
    }

    /**
     * 根据名称查找角色
     */
    default Optional<SystemRole> findByName(String name, @Nullable Fetcher<SystemRole> fetcher) {
        return sql().createQuery(table)
                .where(table.name().eq(name))
                .where(table.deleted().eq(false))
                .select(table.fetch(fetcher))
                .execute()
                .stream()
                .findFirst();
    }

    /**
     * 检查角色名称是否存在
     */
    default boolean existsByName(String name) {
        return sql().createQuery(table)
                .where(table.name().eq(name))
                .where(table.deleted().eq(false))
                .exists();
    }

    /**
     * 检查角色名称是否存在（排除指定ID）
     */
    default boolean existsByNameAndIdNot(String name, @Nullable Long excludeId) {
        var query = sql().createQuery(table)
                .where(table.name().eq(name))
                .where(table.deleted().eq(false));

        if (excludeId != null) {
            query = query.where(table.id().ne(excludeId));
        }

        return query.exists();
    }

    /**
     * 查询所有可用角色
     */
    default List<SystemRole> findAllAvailable(@Nullable Fetcher<SystemRole> fetcher) {
        return sql().createQuery(table)
                .where(table.disabled().eq(false))
                .where(table.deleted().eq(false))
                .orderBy(table.sortNum().asc())
                .select(table.fetch(fetcher))
                .execute();
    }

    /**
     * 检查角色是否被用户使用
     */
    default boolean isRoleInUse(Long roleId) {
        var assoc = AssociationTable.of(SystemUserTableEx.class, SystemUserTableEx::roles);
        return sql().createAssociationQuery(assoc)
                .where(assoc.target().id().eq(roleId))
                .exists();
    }

    /**
     * 软删除角色
     */
    default boolean softDelete(Long roleId) {
        SystemRole updatedRole = Immutables.createSystemRole(draft -> {
            draft.setId(roleId);
            draft.setDeleted(true);
        });

        SimpleSaveResult<SystemRole> result = sql().saveCommand(updatedRole)
                .execute();

        return result.getTotalAffectedRowCount() == 1;
    }

    /**
     * 批量软删除角色
     */
    default int softDeleteBatch(List<Long> roleIds) {
        return sql().createUpdate(table)
                .set(table.deleted(), true)
                .where(table.id().in(roleIds))
                .execute();
    }

    /**
     * 查询角色的权限资源
     */
    default List<SystemResource> findRoleResources(long roleId, @Nullable Fetcher<SystemResource> fetcher) {
        var assoc = AssociationTable.of(SystemRoleTableEx.class, SystemRoleTableEx::resources);
        return sql().createAssociationQuery(assoc)
                .where(assoc.source().id().eq(roleId))
                .select(assoc.target().fetch(fetcher))
                .execute();
    }

    /**
     * 为角色分配权限资源
     */
    default void assignResources(long roleId, List<Long> resourceIds) {
        // 使用Jimmer的save方法来管理关联关系
        SystemRole role = Immutables.createSystemRole(draft -> {
            draft.setId(roleId);
            List<SystemResource> resources = resourceIds.stream()
                    .map(resourceId -> Immutables.createSystemResource(resourceDraft ->
                            resourceDraft.setId(resourceId)))
                    .toList();
            draft.setResources(resources);
        });

        save(role);
    }

    /**
     * 移除角色的权限资源
     */
    default void removeResources(long roleId, List<Long> resourceIds) {
        // 先获取当前角色的所有资源
        SystemRole currentRole = findById(roleId).orElse(null);
        if (currentRole != null && currentRole.resources() != null) {
            List<SystemResource> remainingResources = currentRole.resources().stream()
                    .filter(resource -> !resourceIds.contains(resource.id()))
                    .toList();

            SystemRole updatedRole = Immutables.createSystemRole(draft -> {
                draft.setId(roleId);
                draft.setResources(remainingResources);
            });

            save(updatedRole);
        }
    }
}