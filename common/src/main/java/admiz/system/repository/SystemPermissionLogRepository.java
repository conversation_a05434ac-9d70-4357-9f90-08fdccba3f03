package admiz.system.repository;

import admiz.system.model.SystemPermissionLog;
import admiz.system.model.SystemPermissionLogTable;
import admiz.system.model.Tables;
import org.babyfish.jimmer.spring.repository.JRepository;
import org.babyfish.jimmer.spring.repository.support.SpringPageFactory;
import org.babyfish.jimmer.sql.ast.tuple.Tuple2;
import org.babyfish.jimmer.sql.ast.tuple.Tuple3;
import org.babyfish.jimmer.sql.fetcher.Fetcher;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;

// 系统权限日志Repository
public interface SystemPermissionLogRepository extends JRepository<SystemPermissionLog, Long> {
    SystemPermissionLogTable table = Tables.SYSTEM_PERMISSION_LOG_TABLE;

    // 分页查询权限日志
    default Page<SystemPermissionLog> paginate(@Nullable String operationType,
                                             @Nullable String operationResult,
                                               @Nullable String recordStatus,
                                             @Nullable Long operatorUserId,
                                             @Nullable Long targetUserId,
                                             @Nullable LocalDateTime startTime,
                                             @Nullable LocalDateTime endTime,
                                             @NotNull Pageable pageable,
                                             @Nullable Fetcher<SystemPermissionLog> fetcher) {
        var query = sql().createQuery(table);

        if (operationType != null) {
            query = query.where(table.operationType().eq(
                    SystemPermissionLog.OperationType.valueOf(operationType)));
        }
        if (operationResult != null) {
            query = query.where(table.operationResult().eq(
                    SystemPermissionLog.OperationResult.valueOf(operationResult)));
        }
        if (recordStatus != null) {
            query = query.where(table.recordStatus().eq(
                    SystemPermissionLog.RecordStatus.valueOf(recordStatus)));
        }
        if (operatorUserId != null) {
            query = query.where(table.operatorUserId().eq(operatorUserId));
        }
        if (targetUserId != null) {
            query = query.where(table.targetUserId().eq(targetUserId));
        }
        if (startTime != null) {
            query = query.where(table.createTime().ge(startTime));
        }
        if (endTime != null) {
            query = query.where(table.createTime().le(endTime));
        }

        return query.orderBy(table.createTime().desc())
                .select(table.fetch(fetcher))
                .fetchPage(pageable.getPageNumber(), pageable.getPageSize(), SpringPageFactory.getInstance());
    }

    // 根据操作用户查询日志
    default List<SystemPermissionLog> findByOperatorUserId(@NotNull Long operatorUserId,
                                                          @Nullable Fetcher<SystemPermissionLog> fetcher) {
        return sql().createQuery(table)
                .where(table.operatorUserId().eq(operatorUserId))
                .orderBy(table.createTime().desc())
                .select(table.fetch(fetcher))
                .execute();
    }

    // 根据目标用户查询日志
    default List<SystemPermissionLog> findByTargetUserId(@NotNull Long targetUserId,
                                                        @Nullable Fetcher<SystemPermissionLog> fetcher) {
        return sql().createQuery(table)
                .where(table.targetUserId().eq(targetUserId))
                .orderBy(table.createTime().desc())
                .select(table.fetch(fetcher))
                .execute();
    }

    // 根据操作类型查询日志
    default List<SystemPermissionLog> findByOperationType(@NotNull SystemPermissionLog.OperationType operationType,
                                                         @Nullable Fetcher<SystemPermissionLog> fetcher) {
        return sql().createQuery(table)
                .where(table.operationType().eq(operationType))
                .orderBy(table.createTime().desc())
                .select(table.fetch(fetcher))
                .execute();
    }

    // 查询指定时间范围内的日志
    default List<SystemPermissionLog> findByTimeRange(@NotNull LocalDateTime startTime,
                                                     @NotNull LocalDateTime endTime,
                                                     @Nullable Fetcher<SystemPermissionLog> fetcher) {
        return sql().createQuery(table)
                .where(table.createTime().between(startTime, endTime))
                .orderBy(table.createTime().desc())
                .select(table.fetch(fetcher))
                .execute();
    }

    // 查询失败的权限操作
    default List<SystemPermissionLog> findFailedOperations(@Nullable LocalDateTime since,
                                                          @Nullable Fetcher<SystemPermissionLog> fetcher) {
        var query = sql().createQuery(table)
                .where(table.operationResult().eq(SystemPermissionLog.OperationResult.FAILED));

        if (since != null) {
            query = query.where(table.createTime().ge(since));
        }

        return query.orderBy(table.createTime().desc())
                .select(table.fetch(fetcher))
                .execute();
    }

    // 统计操作类型分布
    default List<Tuple2<SystemPermissionLog.OperationType, Long>> countByOperationType(@Nullable LocalDateTime startTime,
                                                                                       @Nullable LocalDateTime endTime) {
        var query = sql().createQuery(table);

        if (startTime != null) {
            query = query.where(table.createTime().ge(startTime));
        }
        if (endTime != null) {
            query = query.where(table.createTime().le(endTime));
        }

        return query.groupBy(table.operationType())
                .select(
                        table.operationType(),
                        table.id().count()
                )
                .execute();
    }

    // 统计用户操作次数
    default List<Tuple3<Long, String, Long>> countByOperatorUser(@Nullable LocalDateTime startTime,
                                                                 @Nullable LocalDateTime endTime,
                                                                 int limit) {
        var query = sql().createQuery(table);

        if (startTime != null) {
            query = query.where(table.createTime().ge(startTime));
        }
        if (endTime != null) {
            query = query.where(table.createTime().le(endTime));
        }

        return query.where(table.operatorUserId().isNotNull())
                .groupBy(table.operatorUserId(), table.operatorUserLogin())
                .orderBy(table.id().count().desc())
                .select(
                        table.operatorUserId(),
                        table.operatorUserLogin(),
                        table.id().count()
                )
                .limit(limit)
                .execute();
    }

    // 删除指定时间之前的日志
    default int deleteByCreateTimeBefore(@NotNull LocalDateTime beforeTime) {
        return sql().createDelete(table)
                .where(table.createTime().lt(beforeTime))
                .execute();
    }

    // 查询最近的权限检查失败记录
    default List<SystemPermissionLog> findRecentPermissionDenied(@NotNull Long userId,
                                                               @NotNull LocalDateTime since,
                                                               @Nullable Fetcher<SystemPermissionLog> fetcher) {
        return sql().createQuery(table)
                .where(table.operationType().eq(SystemPermissionLog.OperationType.PERMISSION_CHECK))
                .where(table.operationResult().eq(SystemPermissionLog.OperationResult.DENIED))
                .where(table.operatorUserId().eq(userId))
                .where(table.createTime().ge(since))
                .orderBy(table.createTime().desc())
                .select(table.fetch(fetcher))
                .execute();
    }
}
