package admiz.system.repository;

import admiz.system.model.SystemKvStore;
import admiz.system.model.SystemKvStoreTable;
import admiz.system.model.Tables;
import org.babyfish.jimmer.spring.repository.JRepository;

import java.time.LocalDateTime;

public interface SystemKvStoreRepository extends JRepository<SystemKvStore, String> {
    SystemKvStoreTable table = Tables.SYSTEM_KV_STORE_TABLE;

    default boolean existsByIdAndData(String id, String data) {
        return sql().createQuery(table)
                .where(table.id().eq(id))
                .where(table.data().eq(data))
                .exists();

    }
    default int removeAllByExpireTime(LocalDateTime priorToDateTime) {
        return sql().createDelete(table)
                .where(table.expireTime().lt(priorToDateTime))
                .execute();
    }
}
