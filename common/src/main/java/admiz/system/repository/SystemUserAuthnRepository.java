package admiz.system.repository;

import admiz.system.model.Fetchers;
import admiz.system.model.SystemUserAuthn;
import admiz.system.model.SystemUserAuthnTable;
import admiz.system.model.Tables;
import lombok.NonNull;
import org.babyfish.jimmer.spring.repository.JRepository;

import java.util.List;
import java.util.Optional;

public interface SystemUserAuthnRepository extends JRepository<SystemUserAuthn, Long> {
    SystemUserAuthnTable table = Tables.SYSTEM_USER_AUTHN_TABLE;

    // Optional<SystemUserAuthn> findByAuthnIdAndAuthnType(@NonNull String authnId, SystemUserAuthn.AuthnType authnType);
    // List<SystemUserAuthn> findByLogin(String login);


    default Optional<SystemUserAuthn> findByUserIdAndAuthnType(long userId, SystemUserAuthn.AuthnType authnType) {
        return sql().createQuery(table)
                .where(table.userId().eq(userId))
                .where(table.authnType().eq(authnType))
                .select(table)
                .execute()
                .stream()
                .findFirst();
    }

    default Optional<SystemUserAuthn> findByAuthnIdAndAuthnType(@NonNull String authnId, SystemUserAuthn.AuthnType authnType) {
        return sql().createQuery(table)
                .where(table.authnId().eq(authnId))
                .where(table.authnType().eq(authnType))
                .select(table)
                .execute()
                .stream()
                .findFirst();
    }
}
