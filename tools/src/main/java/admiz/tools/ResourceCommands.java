package admiz.tools;

import admiz.common.DataRealm;
import admiz.system.model.*;
import admiz.system.repository.SystemResourceRepository;
import admiz.system.repository.SystemResourceRepository;
import admiz.system.repository.SystemUserRepository;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.apache.shiro.authc.credential.DefaultPasswordService;
import org.springframework.shell.command.annotation.Command;
import org.springframework.shell.command.annotation.Option;
import org.springframework.shell.table.*;
import org.springframework.shell.table.Tables;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Command(group = "Resource") @Slf4j
public class ResourceCommands {
    final SystemResourceRepository resourceRepository;

    @SneakyThrows
    @Command(command = "res:delete", description = "删除资源")
    public void delete(@Option String login) {
        int rowsAffected = resourceRepository.deleteByName(login);
        log.info("Resource delete: {}", rowsAffected);
    }

    @SneakyThrows
    @Command(command = "res:delete2", description = "删除资源")
    public void delete2(@Option String login) {
        int rowsAffected = resourceRepository.deleteByNameAndPermission(null, null);
        log.info("Resource delete: {}", rowsAffected);
    }

}
