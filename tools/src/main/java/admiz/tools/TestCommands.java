package admiz.tools;

import admiz.common.i18n.I18n;
import admiz.common.objectMapper.SimpleObjectMapper;
import admiz.system.model.SystemUserAuthn;
import admiz.system.repository.SystemResourceRepository;
import admiz.system.repository.SystemUserAuthnRepository;
import admiz.system.repository.SystemUserRepository;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.shell.command.annotation.Command;
import org.springframework.shell.command.annotation.Option;
import org.springframework.shell.standard.ShellComponent;
import org.springframework.shell.standard.ShellMethod;
import org.springframework.shell.standard.ShellOption;

import java.net.InetAddress;
import java.util.Locale;
import java.util.ResourceBundle;

@Slf4j
@Command(group = "Test")
@RequiredArgsConstructor
public class TestCommands {
    final SystemUserRepository userRepository;
    final SystemUserAuthnRepository authnRepository;
    final SystemResourceRepository resourceRepository;
    final MessageSource messageSource;

    @SneakyThrows
    @Command(command = "test:ser")
    public void ser(@Option String login) {
        SystemUserAuthn authn = authnRepository.findByAuthnIdAndAuthnType(login, SystemUserAuthn.AuthnType.PASSWORD).orElseThrow();
        String json = SimpleObjectMapper.SHARED_MAPPER.writeValueAsString(authn);
        log.info("UserAuthn json: {}", json);

        SystemUserAuthn systemUserAuthn = SimpleObjectMapper.SHARED_MAPPER.readValue(json, SystemUserAuthn.class);
        log.info("systemUserAuthn: {}", systemUserAuthn);

    }


    @SneakyThrows
    @Command(command = "test:i18n")
    public void i18n(@Option String key) {
        String welcomeMessageSource = messageSource.getMessage("welcome", null, Locale.CHINA);
        log.info("i18n: welcomeMessageSource={}", welcomeMessageSource);

        ResourceBundle bundle = ResourceBundle.getBundle("i18n.messages", Locale.CHINA);
        Object welcomeResourceBundle = bundle.getObject("welcome");
        log.info("i18n: welcomeResourceBundle={}", welcomeResourceBundle);

        ResourceBundle bundle2 = ResourceBundle.getBundle("i18n/messages", Locale.CHINA);
        Object welcomeResourceBundle2 = bundle2.getObject("welcome");
        log.info("i18n: welcomeResourceBundle2={}", welcomeResourceBundle2);
    }

}
