package admiz.tools;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.shell.standard.ShellComponent;
import org.springframework.shell.standard.ShellMethod;
import org.springframework.shell.standard.ShellOption;

import java.net.InetAddress;

@ShellComponent @Slf4j
public class Commands {
    @ShellMethod(key = "hello-world")
    public String helloWorld(@ShellOption(defaultValue = "spring") String arg) {
        return "Hello world " + arg;
    }

    @SneakyThrows
    @ShellMethod(key = "resolve")
    public void resolve(@ShellOption String name) {
        InetAddress[] addresses = InetAddress.getAllByName(name);
        System.out.println("resolve result:");
        for (InetAddress address : addresses) {
            System.out.println("  " + address.getHostAddress());
        }

        System.out.println();
    }


}
