package admiz.tools;

import admiz.system.model.SystemResource;
import admiz.system.repository.SystemResourceRepository;
import org.babyfish.jimmer.spring.repository.EnableJimmerRepositories;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.shell.command.annotation.CommandScan;

import java.util.List;

@SpringBootApplication @CommandScan("admiz.tools") @Configuration
@EnableJimmerRepositories(basePackages = {"admiz.system.repository"})
@ComponentScan(basePackages = {"admiz.tools", "admiz.system.repository"})
public class CliApp {
    public static void main(String[] args) {
        String currentDir = System.getProperty("user.dir");
        System.out.println("Current working directory: " + currentDir);
        // SpringApplication.run(CliApp.class, args);
        ConfigurableApplicationContext applicationContext = SpringApplication.run(CliApp.class, args);
    }

    @Bean
    public ReloadableResourceBundleMessageSource messageSource() {

        ReloadableResourceBundleMessageSource messageSource = new ReloadableResourceBundleMessageSource();
        messageSource.setBasename("file:/Users/<USER>/projects/aip/ly-admin/tools/src/zzz");
        messageSource.setDefaultEncoding("UTF-8");
        messageSource.setUseCodeAsDefaultMessage(true);
        messageSource.setCacheMillis(10000);
        return messageSource;
    }
}
