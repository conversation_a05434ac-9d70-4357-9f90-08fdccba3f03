package admiz.tools;

import admiz.common.DataRealm;
import admiz.common.auth.SecurityMixin;
import admiz.system.model.*;
import admiz.system.repository.SystemResourceRepository;
import admiz.system.repository.SystemUserAuthnRepository;
import admiz.system.repository.SystemUserRepository;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.babyfish.jimmer.sql.ast.mutation.SaveMode;
import org.springframework.shell.command.annotation.Command;
import org.springframework.shell.command.annotation.Option;
import org.springframework.shell.table.*;
import org.springframework.shell.table.Tables;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Command(group = "User") @Slf4j
public class UserCommands implements SecurityMixin {
    final SystemUserRepository userRepository;
    final SystemUserAuthnRepository authnRepository;
    final SystemResourceRepository resourceRepository;

    @SneakyThrows
    @Command(command = "user:add", description = "添加用户")
    public void add(@Option String login, @Option String password, @Option(required = false) String roles) {
        log.info("login={}, password={}, roles={}", login, password, roles);
        List<SystemRole> roleList = Arrays.stream(Strings.isBlank(roles) ? Strings.EMPTY_ARRAY : roles.split(",")).map(String::trim).map(this::createRole).toList();
        SystemUser user = Immutables.createSystemUser(draft -> {
            draft.setLogin(login);
            draft.setDisplayName(login);
            draft.setRealm(DataRealm.SYSTEM);
            draft.setRoles(roleList);
        });

        SystemUser saved = userRepository.save(user);

        String encryptedPassword = getPasswordService().encryptPassword(password);
        SystemUserAuthn userAuthn = Immutables.createSystemUserAuthn(draft -> {
            draft.setUserId(saved.id())
                .setAuthnId(login)
                .setAuthnType(SystemUserAuthn.AuthnType.PASSWORD)
                .setAuthnData(encryptedPassword)
                .setUpdateTime(LocalDateTime.now());
        });

        SystemUserAuthn savedUserAuthn = authnRepository.save(userAuthn);
        log.info("UserAuthn created: {}", savedUserAuthn);
    }

    private SystemRole createRole(String role) {
        return Immutables.createSystemRole(draft -> {
            draft.setName(role);
            draft.setDisplayName(role);
        });
    }

    @SneakyThrows
    @Command(command = "user:find", description = "列出用户")
    public void find(@Option(required = false) String login, @Option(defaultValue = "10") int num) {
        SystemUserFetcher userFetcher = Fetchers.SYSTEM_USER_FETCHER
                .login()
                .displayName()
                .realm()
                .locked()
                .roles(Fetchers.SYSTEM_ROLE_FETCHER.name())
                ;
        List<SystemUser> userList = userRepository.findByLoginStartsWith(login, userFetcher);
        List<Object[]> userInfos = userList.stream()
                .map(u -> {
                    String roles = u.roles().stream().map(SystemRole::name).collect(Collectors.joining(", "));
                    String authnSources =  authnRepository.findByAuthnIdAndAuthnType(u.login(), SystemUserAuthn.AuthnType.PASSWORD).stream().map(SystemUserAuthn::authnType).map(SystemUserAuthn.AuthnType::value).collect(Collectors.joining(", "));

                    return new Object[]{
                            u.id(), u.login(), u.displayName(), u.isBuiltinAdmin(), u.realm(), u.locked(), roles, authnSources
                    };
                })
                .toList();

        List<Object[]> rows = new ArrayList<>();

        Object[] header = {"ID", "LOGIN", "DISPLAY_NAME", "IS_ADMIN", "REALM", "DISABLED", "LOCKED", "ROLES", "AUTHN_SOURCES"};
        rows.add(header);
        rows.addAll(userInfos);

        TableModel model = new ArrayTableModel(rows.toArray(new Object[][]{}));
        TableBuilder tableBuilder = new TableBuilder(model)
                .addFullBorder(BorderStyle.fancy_light);
        Tables.configureKeyValueRendering(tableBuilder, " = ");
        Table table = tableBuilder.build();
        String result = table.render(150);
        System.out.println(result);
    }


    @SneakyThrows
    @Command(command = "user:auth", description = "添加用户")
    public void auth(@Option String login, @Option(required = true) String password) {
        SystemUserAuthn authn = authnRepository.findByAuthnIdAndAuthnType(login, SystemUserAuthn.AuthnType.PASSWORD).orElseThrow();
        boolean matched = getPasswordService().passwordsMatch(password, authn.authnData());
        log.info("UserAuthn result: {}", matched);
    }

    @SneakyThrows
    @Command(group = "user", command = "chpass", description = "修改用户密码")
    public void chpass(@Option(required = true) String login, @Option(required = true) String password) {
        SystemUserAuthn authn = authnRepository.findByAuthnIdAndAuthnType(login, SystemUserAuthn.AuthnType.PASSWORD).orElseThrow();
        String hashed = getPasswordService().encryptPassword(password);
        SystemUserAuthn updated = Immutables.createSystemUserAuthn(draft -> {
            draft.setUserId(authn.userId())
                    .setAuthnData(hashed)
                    .setUpdateTime(LocalDateTime.now());
        });
        SystemUserAuthn saved = authnRepository.save(updated, SaveMode.NON_IDEMPOTENT_UPSERT);

        log.info("UserAuthn: password changed, hash={}", hashed);
    }

    @SneakyThrows
    @Command(group = "user", command = "password", description = "hash password用户")
    public void hashPassword(@Option(required = true) String password) {
        String matched = getPasswordService().encryptPassword(password);
        log.info("UserAuthn hash result: {}", matched);
    }

    @SneakyThrows
    @Command(command = "user:delete", description = "添加用户")
    public void delete(@Option String login) {
        int rowsAffected = resourceRepository.deleteByName(login);
        log.info("UserAuthn delete: {}", rowsAffected);
    }

    @SneakyThrows
    @Command(command = "user:delete2", description = "添加用户")
    public void delete2(@Option String login) {
        int rowsAffected = resourceRepository.deleteByNameAndPermission(null, null);
        log.info("UserAuthn delete: {}", rowsAffected);
    }

}
