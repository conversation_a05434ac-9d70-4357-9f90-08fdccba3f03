<div class="toast-container position-fixed top-0 start-50 translate-middle-x p-3" id="toast-container" style="z-index: 9999;"></div>
<div class="card-body p-0">
    <div class="resource-tree" id="resource-tree-container">
        <div class="d-flex justify-content-center p-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
        </div>
    </div>
</div>
<div aria-hidden="true" aria-labelledby="editResourceModalLabel" class="modal fade" id="editResourceModal"
     tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editResourceModalLabel">编辑资源</h5>
                <button aria-label="Close" class="btn-close" data-bs-dismiss="modal" type="button"></button>
            </div>
            <div class="modal-body">
                <form id="editResourceForm">
                    <input id="editResourceId" type="hidden">

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label" for="editResourceName">资源名称 <span class="text-danger">*</span></label>
                            <input class="form-control" id="editResourceName" required type="text">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label" for="editResourceDisplayName">显示名称 <span
                                    class="text-danger">*</span></label>
                            <input class="form-control" id="editResourceDisplayName" required type="text">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label" for="editResourcePermission">权限编码 <span
                                    class="text-danger">*</span></label>
                            <input class="form-control" id="editResourcePermission" required type="text">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label" for="editResourceType">资源类型 <span class="text-danger">*</span></label>
                            <select class="form-select" id="editResourceType" required>
                                <option value="FOLDER">目录</option>
                                <option value="MENU">菜单</option>
                                <option value="FUNCTION">功能</option>
                                <option value="DATA">数据</option>
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label" for="editResourceUrl">URL</label>
                            <input class="form-control" id="editResourceUrl" type="text">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label" for="editResourceTarget">目标</label>
                            <input class="form-control" id="editResourceTarget" type="text">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label" for="editResourceParentId">父级资源</label>
                            <select class="form-select" id="editResourceParentId">
                                <option value="">无父级（顶级资源）</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label" for="editResourceSortNum">排序</label>
                            <input class="form-control" id="editResourceSortNum" type="number" value="0">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label" for="editResourceIcon">图标</label>
                            <input class="form-control" id="editResourceIcon" type="text">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">状态选项</label>
                            <div>
                                <label class="form-check form-check-inline">
                                    <input class="form-check-input" id="editResourceDisabled" type="checkbox">
                                    <span class="form-check-label">禁用</span>
                                </label>
                                <label class="form-check form-check-inline">
                                    <input class="form-check-input" id="editResourceHidden" type="checkbox">
                                    <span class="form-check-label">隐藏</span>
                                </label>
                                <label class="form-check form-check-inline">
                                    <input class="form-check-input" id="editResourceAutoRefresh" type="checkbox">
                                    <span class="form-check-label">自动刷新</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label" for="editResourceRemark">备注</label>
                        <textarea class="form-control" id="editResourceRemark" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" data-bs-dismiss="modal" type="button">取消</button>
                <button class="btn btn-primary" onclick="saveEditResource()" type="button">保存</button>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function () {
        let resourceTree = [];
        let filteredTree = [];
        let expandedNodes = new Set();
        let currentFilter = {};

        loadResourceTree();

        $(document).on('filterResourceTree', function (e, filterData) {
            currentFilter = filterData;
            applyFilter();
        });

        $('#btn-create-resource').click(() => {
            window.location.href = '/admiz/system/mgmt/resources/create';
        });

        $('#btn-expand-all').click(() => {
            expandAllNodes();
        });

        $('#btn-collapse-all').click(() => {
            collapseAllNodes();
        });

        function loadResourceTree() {
            $.get('/admiz/api/system/mgmt/resources/tree')
                .done(function (result) {
                    if (result.succeed) {
                        resourceTree = result.data;
                        filteredTree = resourceTree;
                        renderResourceTree();
                    } else {
                        window.showToast('加载资源树失败: ' + result.msg, 'danger');
                    }
                })
                .fail(function () {
                    window.showToast('加载资源树失败，请稍后重试', 'danger');
                });
        }

        function renderResourceTree() {
            const container = $('#resource-tree-container');
            container.empty();

            if (filteredTree.length === 0) {
                container.html(`
                <div class="empty">
                    <div class="empty-icon">
                        <i class="ti ti-folder-x"></i>
                    </div>
                    <p class="empty-title">暂无资源</p>
                    <p class="empty-subtitle text-secondary">点击"创建资源"按钮添加第一个资源</p>
                </div>
            `);
                return;
            }

            const treeHtml = filteredTree.map(resource => renderResourceNode(resource, 0)).join('');
            container.html(treeHtml);

            bindTreeEvents();
        }

        function renderResourceNode(resource, level) {
            const hasChildren = resource.children && resource.children.length > 0;
            const isExpanded = expandedNodes.has(resource.id);
            const indent = level * 20;

            const typeIcon = getResourceTypeIcon(resource.resourceType);
            const statusBadge = getStatusBadge(resource);

            let html = `
            <div class="resource-node" data-resource-id="${resource.id}" data-level="${level}">
                <div class="d-flex align-items-center p-2 border-bottom resource-item" style="padding-left: ${indent + 12}px !important;">
                    <div class="resource-toggle me-2" style="width: 16px;">
                        ${hasChildren ? `<i class="ti ti-chevron-${isExpanded ? 'down' : 'right'} cursor-pointer"></i>` : ''}
                    </div>
                    <div class="resource-icon me-2">
                        ${typeIcon}
                    </div>
                    <div class="flex-fill">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center">
                                <strong class="resource-name me-2">${resource.displayName || resource.name}</strong>
                                ${statusBadge}
                                <span class="badge bg-secondary-lt text-secondary ms-2">${getResourceTypeText(resource.resourceType)}</span>
                            </div>
                            <div class="resource-actions">
                                <button class="btn btn-outline-secondary btn-edit-resource"
                                        data-resource-id="${resource.id}"
                                        title="编辑">
                                    <i class="ti ti-edit"></i>
                                </button>
                                <button class="btn btn-outline-danger ms-1 btn-delete-resource"
                                        data-resource-id="${resource.id}"
                                        data-resource-name="${resource.displayName || resource.name}"
                                        title="删除">
                                    <i class="ti ti-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="text-secondary small mt-1">
                            <span class="me-3">权限: ${resource.permission}</span>
                            ${resource.url ? `<span class="me-3">URL: ${resource.url}</span>` : ''}
                            ${resource.parentId ? `<span class="me-3">父级ID: ${resource.parentId}</span>` : ''}
                            <span>排序: ${resource.sortNum}</span>
                        </div>
                    </div>
                </div>
        `;

            if (hasChildren && isExpanded) {
                html += '<div class="resource-children">';
                resource.children.forEach(child => {
                    html += renderResourceNode(child, level + 1);
                });
                html += '</div>';
            }

            html += '</div>';
            return html;
        }

        function getResourceTypeIcon(resourceType) {
            const icons = {
                'FOLDER': '<i class="ti ti-folder text-warning"></i>',
                'MENU': '<i class="ti ti-menu-2 text-primary"></i>',
                'FUNCTION': '<i class="ti ti-tool text-info"></i>',
                'DATA': '<i class="ti ti-database text-success"></i>'
            };
            return icons[resourceType] || '<i class="ti ti-file"></i>';
        }

        function getStatusBadge(resource) {
            if (resource.disabled) {
                return '<span class="badge bg-red-lt text-red ms-2">已禁用</span>';
            }
            if (resource.hidden) {
                return '<span class="badge bg-yellow-lt text-yellow ms-2">已隐藏</span>';
            }
            return '<span class="badge bg-green-lt text-green ms-2">正常</span>';
        }

        function getResourceTypeText(resourceType) {
            const typeTexts = {
                'FOLDER': '目录',
                'MENU': '菜单',
                'FUNCTION': '功能',
                'DATA': '数据'
            };
            return typeTexts[resourceType] || resourceType;
        }

        function bindTreeEvents() {
            $('.resource-toggle i').click(function (e) {
                e.stopPropagation();
                const resourceNode = $(this).closest('.resource-node');
                const resourceId = parseInt(resourceNode.data('resource-id'));
                toggleNode(resourceId);
            });

            $('.btn-edit-resource').click(function (e) {
                e.stopPropagation();
                const resourceId = $(this).data('resource-id');
                openEditModal(resourceId);
            });

            $('.btn-delete-resource').click(function (e) {
                e.stopPropagation();
                const resourceId = $(this).data('resource-id');
                const resourceName = $(this).data('resource-name');

                if (confirm(`确定要删除资源 "${resourceName}" 吗？此操作不可恢复。`)) {
                    deleteResource(resourceId);
                }
            });
        }

        function toggleNode(resourceId) {
            if (expandedNodes.has(resourceId)) {
                expandedNodes.delete(resourceId);
            } else {
                expandedNodes.add(resourceId);
            }
            renderResourceTree();
        }

        function expandAllNodes() {
            function addAllIds(resources) {
                resources.forEach(resource => {
                    if (resource.children && resource.children.length > 0) {
                        expandedNodes.add(resource.id);
                        addAllIds(resource.children);
                    }
                });
            }

            addAllIds(filteredTree);
            renderResourceTree();
        }

        function applyFilter() {
            if (!currentFilter.searchText && !currentFilter.resourceType && !currentFilter.disabled) {
                filteredTree = resourceTree;
            } else {
                filteredTree = filterTreeNodes(resourceTree);
            }
            renderResourceTree();
        }

        function filterTreeNodes(nodes) {
            return nodes.filter(node => {
                const matchesFilter = matchesCurrentFilter(node);
                const hasMatchingChildren = node.children && filterTreeNodes(node.children).length > 0;

                if (matchesFilter || hasMatchingChildren) {
                    const filteredNode = {...node};
                    if (node.children) {
                        filteredNode.children = filterTreeNodes(node.children);
                    }
                    return filteredNode;
                }
                return null;
            }).filter(node => node !== null);
        }

        function matchesCurrentFilter(node) {
            if (currentFilter.searchText) {
                const searchText = currentFilter.searchText.toLowerCase();
                const nameMatch = (node.name || '').toLowerCase().includes(searchText);
                const displayNameMatch = (node.displayName || '').toLowerCase().includes(searchText);
                const permissionMatch = (node.permission || '').toLowerCase().includes(searchText);

                if (!nameMatch && !displayNameMatch && !permissionMatch) {
                    return false;
                }
            }

            if (currentFilter.resourceType && node.resourceType !== currentFilter.resourceType) {
                return false;
            }

            if (currentFilter.disabled !== '') {
                const isDisabled = currentFilter.disabled === 'true';
                if (node.disabled !== isDisabled) {
                    return false;
                }
            }

            return true;
        }

        function collapseAllNodes() {
            expandedNodes.clear();
            renderResourceTree();
        }

        function deleteResource(resourceId) {
            $.ajax({
                url: `/admiz/api/system/mgmt/resources/${resourceId}`,
                method: 'DELETE'
            })
                .done((result) => {
                    if (result.succeed) {
                        window.showToast(result.msg || '删除成功', 'success');
                        loadResourceTree();
                    } else {
                        window.showToast('删除失败: ' + result.msg, 'danger');
                    }
                })
                .fail((xhr) => {
                    window.showToast('删除失败，请稍后重试', 'danger');
                });
        }



        function openEditModal(resourceId) {
            $.get(`/admiz/api/system/mgmt/resources/${resourceId}`)
                .done(function (result) {
                    if (result.succeed) {
                        showEditModal(result.data);
                    } else {
                        window.showToast('获取资源详情失败: ' + result.msg, 'danger');
                    }
                })
                .fail(function () {
                    window.showToast('获取资源详情失败，请稍后重试', 'danger');
                });
        }

        function showEditModal(resource) {
            const modal = $('#editResourceModal');

            loadParentResourceOptions(resource.id, function () {
                $('#editResourceId').val(resource.id);
                $('#editResourceName').val(resource.name);
                $('#editResourceDisplayName').val(resource.displayName);
                $('#editResourcePermission').val(resource.permission);
                $('#editResourceUrl').val(resource.url || '');
                $('#editResourceTarget').val(resource.target || '');
                $('#editResourceType').val(resource.resourceType);
                $('#editResourceParentId').val(resource.parentId || '');
                $('#editResourceSortNum').val(resource.sortNum);
                $('#editResourceDisabled').prop('checked', resource.disabled);
                $('#editResourceHidden').prop('checked', resource.hidden);
                $('#editResourceAutoRefresh').prop('checked', resource.autoRefresh);
                $('#editResourceIcon').val(resource.icon || '');
                $('#editResourceRemark').val(resource.remark || '');

                modal.modal('show');
            });
        }

        function loadParentResourceOptions(excludeId, callback) {
            $.get('/admiz/api/system/mgmt/resources')
                .done(function (result) {
                    if (result.succeed) {
                        const parentSelect = $('#editResourceParentId');
                        parentSelect.empty();
                        parentSelect.append('<option value="">无父级（顶级资源）</option>');

                        const parentResources = result.data.content.filter(r =>
                            r.id !== excludeId &&
                            (r.resourceType === 'FOLDER' || r.resourceType === 'MENU')
                        );

                        parentResources.forEach(resource => {
                            parentSelect.append(`<option value="${resource.id}">${resource.displayName || resource.name}</option>`);
                        });

                        if (callback) callback();
                    } else {
                        window.showToast('加载父级资源失败: ' + result.msg, 'danger');
                    }
                })
                .fail(function () {
                    window.showToast('加载父级资源失败，请稍后重试', 'danger');
                });
        }


    });

    // 全局函数，供 onclick 事件调用
    window.showToast = function(message, type = 'success') {
        $('#toast-container').showToast(message, type).autoDismiss();
    };

    window.saveEditResource = function() {
        const formData = {
            id: parseInt($('#editResourceId').val()),
            name: $('#editResourceName').val(),
            displayName: $('#editResourceDisplayName').val(),
            permission: $('#editResourcePermission').val(),
            url: $('#editResourceUrl').val(),
            target: $('#editResourceTarget').val(),
            resourceType: $('#editResourceType').val(),
            parentId: $('#editResourceParentId').val() || null,
            sortNum: parseInt($('#editResourceSortNum').val()) || 0,
            disabled: $('#editResourceDisabled').prop('checked'),
            hidden: $('#editResourceHidden').prop('checked'),
            autoRefresh: $('#editResourceAutoRefresh').prop('checked'),
            icon: $('#editResourceIcon').val(),
            remark: $('#editResourceRemark').val()
        };

        $.ajax({
            url: `/admiz/api/system/mgmt/resources/${formData.id}`,
            method: 'PUT',
            contentType: 'application/json',
            data: JSON.stringify(formData)
        })
            .done(function (result) {
                if (result.succeed) {
                    showToast('更新成功', 'success');
                    $('#editResourceModal').modal('hide');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showToast('更新失败: ' + result.msg, 'danger');
                }
            })
            .fail(function () {
                showToast('更新失败，请稍后重试', 'danger');
            });
    };
</script>

<style>
    .resource-tree {
        max-height: 85vh;
        overflow-y: auto;
    }

    .resource-item {
        transition: background-color 0.2s;
    }

    .resource-item:hover {
        background-color: var(--tblr-bg-surface-secondary);
    }

    .resource-toggle {
        cursor: pointer;
    }

    .resource-actions {
        opacity: 0.7;
        transition: opacity 0.2s;
    }

    .resource-item:hover .resource-actions {
        opacity: 1;
    }

    .resource-actions .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }

    .cursor-pointer {
        cursor: pointer;
    }
</style>
