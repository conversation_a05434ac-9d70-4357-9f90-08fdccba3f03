<div class="card">
    <div class="card-stamp">
        <div class="card-stamp-icon bg-white text-primary"><i class="ti ti-filter"></i></div>
    </div>
    <div class="card-body p-2">
        <div class="container-fluid">
            <div class="row m-1" id="filterForm">
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text">搜索</span>
                        <input autocomplete="off" class="form-control" id="searchText" placeholder="资源名称/权限编码"
                               type="text">
                    </div>
                </div>
                <div class="col-auto">
                    <div class="input-group">
                        <span class="input-group-text">类型</span>
                        <select class="form-select" id="resourceType">
                            <option value="">所有类型</option>
                            <option value="FOLDER">目录</option>
                            <option value="MENU">菜单</option>
                            <option value="FUNCTION">功能</option>
                            <option value="DATA">数据</option>
                        </select>
                    </div>
                </div>
                <div class="col-auto">
                    <div class="input-group">
                        <span class="input-group-text">状态</span>
                        <select class="form-select" id="disabled">
                            <option value="">所有状态</option>
                            <option value="false">启用</option>
                            <option value="true">禁用</option>
                        </select>
                    </div>
                </div>
                <div class="col-auto">
                    <button class="btn btn-primary" id="btn-search" type="button">
                        <i class="ti ti-search"></i> 搜索
                    </button>
                    <button class="btn btn-secondary" id="btn-reset-filter" type="button">重置</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(() => {
        // 搜索按钮
        $('#btn-search').click(() => {
            filterResourceTree();
        });

        // 重置过滤器
        $('#btn-reset-filter').click(() => {
            $('#searchText').val('');
            $('#resourceType').val('');
            $('#disabled').val('');
            filterResourceTree();
        });

        // 回车搜索
        $('#searchText').keypress(function (e) {
            if (e.which === 13) {
                filterResourceTree();
            }
        });

        // 过滤器变化时自动搜索
        $('#resourceType, #disabled').change(() => {
            filterResourceTree();
        });

        // 过滤资源树
        function filterResourceTree() {
            const searchText = $('#searchText').val().trim();
            const resourceType = $('#resourceType').val();
            const disabled = $('#disabled').val();

            // 触发树状视图的过滤事件
            $(document).trigger('filterResourceTree', {
                searchText: searchText,
                resourceType: resourceType,
                disabled: disabled
            });
        }
    });
</script>
